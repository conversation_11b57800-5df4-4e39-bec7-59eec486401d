## Autogenerated by Scribe. DO NOT MODIFY.

name: Endpoints
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: ''
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"success":true,"message":"DeliveryNexus API","version":"1.0.0","documentation":"http:\/\/deliverynexus.test\/docs"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: b6a530be-c5d8-4512-9c0f-73561fd66dc5
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-Bf+e/k37y+7jhPsLf1/j5Q==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/web/login
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Smart login for web dashboard (detects central vs tenant context).'
      description: "Uses Laravel Sanctum's built-in stateful authentication."
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      email:
        name: email
        description: 'Must be a valid email address. Must not be greater than 255 characters.'
        required: true
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      password:
        name: password
        description: 'Must be at least 1 character.'
        required: true
        example: '+-0pBNvYgxwmi/#iw'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      device_name:
        name: device_name
        description: 'Must not be greater than 255 characters.'
        required: false
        example: u
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      remember_me:
        name: remember_me
        description: ''
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      email: <EMAIL>
      password: '+-0pBNvYgxwmi/#iw'
      device_name: u
      remember_me: true
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/web/password/reset
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Send password reset code.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      identifier:
        name: identifier
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      identifier: architecto
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/web/password/confirm
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Reset password with token.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      identifier:
        name: identifier
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      token:
        name: token
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      password:
        name: password
        description: 'Must be at least 8 characters.'
        required: true
        example: ']|{+-0pBNvYg'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      identifier: architecto
      token: architecto
      password: ']|{+-0pBNvYg'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/web/register
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Register a new user (Phase 1: Simple email + password).'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 'Full name of the user. Must not be greater than 255 characters.'
        required: true
        example: 'John Doe'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      email:
        name: email
        description: 'Email address for the account. Must be a valid email address. Must not be greater than 255 characters.'
        required: true
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      password:
        name: password
        description: 'Password for the account.'
        required: true
        example: SecurePassword123!
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      phone_number:
        name: phone_number
        description: 'Nigerian phone number (optional, format: +234XXXXXXXXXX). Must match the regex /^\+234[0-9]{10}$/.'
        required: false
        example: '+*************'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      timezone:
        name: timezone
        description: 'User timezone (optional, defaults to Africa/Lagos). Must be a valid time zone, such as <code>Africa/Accra</code>.'
        required: false
        example: Africa/Lagos
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      first_name:
        name: first_name
        description: 'First name (internal field, auto-extracted from name). Must not be greater than 255 characters.'
        required: false
        example: John
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      last_name:
        name: last_name
        description: 'Last name (internal field, auto-extracted from name). Must not be greater than 255 characters.'
        required: false
        example: Doe
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      name: 'John Doe'
      email: <EMAIL>
      password: SecurePassword123!
      phone_number: '+*************'
      timezone: Africa/Lagos
      first_name: John
      last_name: Doe
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/web/select-account-type
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Select account type (Phase 2).'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      account_type:
        name: account_type
        description: 'Type of account to create - "customer", "business", or "delivery_provider".'
        required: true
        example: customer
        type: string
        enumValues:
          - customer
          - business
          - delivery_provider
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      account_type: customer
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/web/onboard/business
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Business onboarding (Phase 3).'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      business_name:
        name: business_name
        description: 'Name of the business. Must be at least 2 characters. Must not be greater than 100 characters.'
        required: true
        example: 'Delicious Eats Restaurant'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      business_type:
        name: business_type
        description: 'Type of business - "food", "retail", "service", "logistics".'
        required: true
        example: food
        type: string
        enumValues:
          - food
          - retail
          - fashion
          - service
          - other
        exampleWasSpecified: false
        nullable: false
        custom: []
      country_id:
        name: country_id
        description: 'Country ID where the business is located. The <code>id</code> of an existing record in the countries table.'
        required: true
        example: a1b2c3d4-e5f6-7890-abcd-ef1234567890
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      description:
        name: description
        description: 'Description of the business (optional). Must not be greater than 500 characters.'
        required: false
        example: 'A family-owned restaurant serving authentic Nigerian cuisine'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      contact_email:
        name: contact_email
        description: 'Contact email for the business (optional). Must be a valid email address. Must not be greater than 100 characters.'
        required: false
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      contact_phone:
        name: contact_phone
        description: 'Contact phone number for the business (optional). Must not be greater than 20 characters.'
        required: false
        example: '+*************'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      business_name: 'Delicious Eats Restaurant'
      business_type: food
      country_id: a1b2c3d4-e5f6-7890-abcd-ef1234567890
      description: 'A family-owned restaurant serving authentic Nigerian cuisine'
      contact_email: <EMAIL>
      contact_phone: '+*************'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/web/onboard/provider
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Provider onboarding (Phase 3).'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      provider_name:
        name: provider_name
        description: 'Name of the delivery provider. Must be at least 2 characters. Must not be greater than 100 characters.'
        required: true
        example: 'Swift Delivery Services'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      provider_type:
        name: provider_type
        description: 'Tier of delivery provider - "individual", "small_fleet", "medium_fleet", "enterprise_fleet".'
        required: true
        example: individual
        type: string
        enumValues:
          - individual
          - small_fleet
          - medium_fleet
          - enterprise_fleet
        exampleWasSpecified: false
        nullable: false
        custom: []
      country_id:
        name: country_id
        description: 'Country ID where the provider operates. The <code>id</code> of an existing record in the countries table.'
        required: true
        example: a1b2c3d4-e5f6-7890-abcd-ef1234567890
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      description:
        name: description
        description: 'Description of the delivery provider (optional). Must not be greater than 500 characters.'
        required: false
        example: 'Professional delivery services covering Lagos and surrounding areas'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      contact_email:
        name: contact_email
        description: 'Contact email for the provider (optional). Must be a valid email address. Must not be greater than 100 characters.'
        required: false
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      contact_phone:
        name: contact_phone
        description: 'Contact phone number for the provider (optional). Must not be greater than 20 characters.'
        required: false
        example: '+*************'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      provider_name: 'Swift Delivery Services'
      provider_type: individual
      country_id: a1b2c3d4-e5f6-7890-abcd-ef1234567890
      description: 'Professional delivery services covering Lagos and surrounding areas'
      contact_email: <EMAIL>
      contact_phone: '+*************'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/web/logout
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Logout user from web dashboard.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/web/logout-all
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Logout from all devices.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/web/refresh
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Refresh session for web dashboard.'
      description: 'For stateful authentication, session refresh is handled automatically by Laravel.'
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/auth/web/me
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get current authenticated user for web dashboard.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:08.701885Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 07a00f63-13c3-4f37-9c2a-bb914f570f47
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-BivmnFbLP8fbMcEnG/yQwA==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
          set-cookie: 'XSRF-TOKEN=eyJpdiI6IkxZZnJ0c241RUErdkUyTytmSCtoQVE9PSIsInZhbHVlIjoibTM5S2s0K1NiR09lcXJUaC80MFNXMnVvV3FvbGsyZGlJTEIrQmRnNFlvNUlOOXhOVm9yMnU3dWc1Sm1QNjFnWWVHWStxWmNnQTVDOE1BTzg0WlVLY2dRa2tkRXB1dERZTFJicnZSQThrM0htSndkOG4rRHFrd0xFK2I0TkcwdkwiLCJtYWMiOiI3NjA2OTk4ZjJmNTExZTY0YjVlYmE4YWViZWI1MTgxZjRiMTQxMTQ0ZDUxZTRhOTBkODQ0MzU5NjlkN2ViYTFiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 11:33:08 GMT; Max-Age=7200; path=/; samesite=lax; deliverynexus_api_session=eyJpdiI6IjJwbVZMSmwyclRkVnZ5L1JqYXQ5alE9PSIsInZhbHVlIjoiZW45UTU2dzlRd0lvNC82UElBTHpKTEt1TS9YeXVGUkg5SWw3dTV5L0FHUjJHMTAxM05DTnkyaDBqRDk1eHUvdm9HRkpSalNMc2lCdy9KWTQwR3B5eVVKRGc3NXlYbkp1RkdxSlNVVjQrek9OZ3lnMnZuYlZBQUNZeWY4ZEM2dS8iLCJtYWMiOiJmYTFhNjczNjc2NTlhOWMzODQyMzBjZDM5MjZiOTA1NTZlMmIyNTdmZmIwODg0Yjg2NzEwODE2ZjgxNTVkYzgyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 11:33:08 GMT; Max-Age=7200; path=/; httponly; samesite=lax'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/web/email/send-verification
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Send email verification code.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/web/email/verify
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Verify email with code.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      code:
        name: code
        description: 'The 6-digit verification code sent to your email. Must match the regex /^[0-9]{6}$/. Must be 6 characters.'
        required: true
        example: '123456'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      code: '123456'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/web/phone/send-verification
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Send phone verification code.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/web/phone/verify
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Verify phone with code.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      code:
        name: code
        description: 'The 6-digit verification code sent to your phone. Must match the regex /^[0-9]{6}$/. Must be 6 characters.'
        required: true
        example: '123456'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      code: '123456'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/health
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Basic health check endpoint.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"success":true,"message":"System is healthy","timestamp":"2025-06-08T09:33:08.716983Z","data":{"status":"ok","service":"DeliveryNexus API","version":"1.0.0"},"request_id":"62848031-1d78-4022-9f85-a3e7dbf1f0ed"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 62848031-1d78-4022-9f85-a3e7dbf1f0ed
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-/dpg1lNE7UUbclPHWiqPEw==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/health/detailed
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Detailed health check with dependencies.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"success":true,"message":"All systems operational","timestamp":"2025-06-08T09:33:08.727707Z","data":{"status":"ok","service":"DeliveryNexus API","version":"1.0.0","checks":{"database":{"status":"ok","message":"Database connection successful","connection":"pgsql","response_time":0.27},"cache":{"status":"ok","message":"Cache working properly","response_time":0.17},"redis":{"status":"ok","message":"Redis working properly","response_time":0.42},"queue":{"status":"ok","message":"Queue connection successful","connection":"redis"}},"system":{"php_version":"8.4.7","laravel_version":"12.16.0","memory_usage":"60.5 MB","disk_usage":"357.9 GB (38.64%)"}},"request_id":"9ce9a032-f25f-4e6c-9cf3-27c2aaf14a86"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 9ce9a032-f25f-4e6c-9cf3-27c2aaf14a86
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-h12j3FdYvWW8zpEAIMtylQ==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/login
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Central domain login - Only allows users with no tenant_id.'
      description: 'Used by customers and platform admins on api.deliverynexus.com'
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      identifier:
        name: identifier
        description: "User's email address or phone number."
        required: true
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      password:
        name: password
        description: "User's password."
        required: true
        example: password123
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      remember_me:
        name: remember_me
        description: 'Whether to remember the user for extended login.'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      identifier: <EMAIL>
      password: password123
      remember_me: false
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/password/reset
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Send password reset code.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      identifier:
        name: identifier
        description: 'Email address or phone number to send password reset instructions.'
        required: true
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      identifier: <EMAIL>
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/password/confirm
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Reset password with token.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      identifier:
        name: identifier
        description: 'Email address or phone number for password reset.'
        required: true
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      token:
        name: token
        description: 'Password reset token received via email/SMS.'
        required: true
        example: abc123def456ghi789jkl012mno345pqr678stu901vwx234yz
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      password:
        name: password
        description: 'New password for the account.'
        required: true
        example: NewSecurePassword123!
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      identifier: <EMAIL>
      token: abc123def456ghi789jkl012mno345pqr678stu901vwx234yz
      password: NewSecurePassword123!
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/user-context
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get user context for mobile app login routing.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      identifier:
        name: identifier
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      identifier: architecto
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/register
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Phase 1: Register a new user (simple email + password only).'
      description: 'Registration only happens on central domain.'
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 'Full name of the user. Must not be greater than 255 characters.'
        required: true
        example: 'John Doe'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      email:
        name: email
        description: 'Email address for the account. Must be a valid email address. Must not be greater than 255 characters.'
        required: true
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      password:
        name: password
        description: 'Password for the account.'
        required: true
        example: SecurePassword123!
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      phone_number:
        name: phone_number
        description: 'Nigerian phone number (optional, format: +234XXXXXXXXXX). Must match the regex /^\+234[0-9]{10}$/.'
        required: false
        example: '+*************'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      timezone:
        name: timezone
        description: 'User timezone (optional, defaults to Africa/Lagos). Must be a valid time zone, such as <code>Africa/Accra</code>.'
        required: false
        example: Africa/Lagos
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      first_name:
        name: first_name
        description: 'First name (internal field, auto-extracted from name). Must not be greater than 255 characters.'
        required: false
        example: John
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      last_name:
        name: last_name
        description: 'Last name (internal field, auto-extracted from name). Must not be greater than 255 characters.'
        required: false
        example: Doe
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      name: 'John Doe'
      email: <EMAIL>
      password: SecurePassword123!
      phone_number: '+*************'
      timezone: Africa/Lagos
      first_name: John
      last_name: Doe
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/select-account-type
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Phase 2: Select account type (customer, business, or delivery provider).'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      account_type:
        name: account_type
        description: 'Type of account to create - "customer", "business", or "delivery_provider".'
        required: true
        example: customer
        type: string
        enumValues:
          - customer
          - business
          - delivery_provider
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      account_type: customer
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/onboard/business
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Phase 3: Business onboarding (creates tenant + business with minimal data).'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      business_name:
        name: business_name
        description: 'Name of the business. Must be at least 2 characters. Must not be greater than 100 characters.'
        required: true
        example: 'Delicious Eats Restaurant'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      business_type:
        name: business_type
        description: 'Type of business - "food", "retail", "service", "logistics".'
        required: true
        example: food
        type: string
        enumValues:
          - food
          - retail
          - fashion
          - service
          - other
        exampleWasSpecified: false
        nullable: false
        custom: []
      country_id:
        name: country_id
        description: 'Country ID where the business is located. The <code>id</code> of an existing record in the countries table.'
        required: true
        example: a1b2c3d4-e5f6-7890-abcd-ef1234567890
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      description:
        name: description
        description: 'Description of the business (optional). Must not be greater than 500 characters.'
        required: false
        example: 'A family-owned restaurant serving authentic Nigerian cuisine'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      contact_email:
        name: contact_email
        description: 'Contact email for the business (optional). Must be a valid email address. Must not be greater than 100 characters.'
        required: false
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      contact_phone:
        name: contact_phone
        description: 'Contact phone number for the business (optional). Must not be greater than 20 characters.'
        required: false
        example: '+*************'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      business_name: 'Delicious Eats Restaurant'
      business_type: food
      country_id: a1b2c3d4-e5f6-7890-abcd-ef1234567890
      description: 'A family-owned restaurant serving authentic Nigerian cuisine'
      contact_email: <EMAIL>
      contact_phone: '+*************'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/onboard/provider
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Phase 3: Provider onboarding (creates tenant + provider with minimal data).'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      provider_name:
        name: provider_name
        description: 'Name of the delivery provider. Must be at least 2 characters. Must not be greater than 100 characters.'
        required: true
        example: 'Swift Delivery Services'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      provider_type:
        name: provider_type
        description: 'Tier of delivery provider - "individual", "small_fleet", "medium_fleet", "enterprise_fleet".'
        required: true
        example: individual
        type: string
        enumValues:
          - individual
          - small_fleet
          - medium_fleet
          - enterprise_fleet
        exampleWasSpecified: false
        nullable: false
        custom: []
      country_id:
        name: country_id
        description: 'Country ID where the provider operates. The <code>id</code> of an existing record in the countries table.'
        required: true
        example: a1b2c3d4-e5f6-7890-abcd-ef1234567890
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      description:
        name: description
        description: 'Description of the delivery provider (optional). Must not be greater than 500 characters.'
        required: false
        example: 'Professional delivery services covering Lagos and surrounding areas'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      contact_email:
        name: contact_email
        description: 'Contact email for the provider (optional). Must be a valid email address. Must not be greater than 100 characters.'
        required: false
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      contact_phone:
        name: contact_phone
        description: 'Contact phone number for the provider (optional). Must not be greater than 20 characters.'
        required: false
        example: '+*************'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      provider_name: 'Swift Delivery Services'
      provider_type: individual
      country_id: a1b2c3d4-e5f6-7890-abcd-ef1234567890
      description: 'Professional delivery services covering Lagos and surrounding areas'
      contact_email: <EMAIL>
      contact_phone: '+*************'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/register/invitation
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Register from staff invitation.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      invitation_token:
        name: invitation_token
        description: 'Invitation token received via email. The <code>token</code> of an existing record in the team_invitations table.'
        required: true
        example: abc123def456ghi789jkl012mno345pqr678stu901vwx234yz
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      first_name:
        name: first_name
        description: 'First name of the staff member. Must be at least 2 characters. Must not be greater than 50 characters.'
        required: true
        example: Jane
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      last_name:
        name: last_name
        description: 'Last name of the staff member. Must be at least 2 characters. Must not be greater than 50 characters.'
        required: true
        example: Smith
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      password:
        name: password
        description: 'Password for the account (minimum 8 characters). Must be at least 8 characters.'
        required: true
        example: SecurePassword123!
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      phone_number:
        name: phone_number
        description: 'Phone number (optional). Must not be greater than 20 characters.'
        required: false
        example: '+*************'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      invitation_token: abc123def456ghi789jkl012mno345pqr678stu901vwx234yz
      first_name: Jane
      last_name: Smith
      password: SecurePassword123!
      phone_number: '+*************'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/showcase/featured-businesses
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get random businesses for homepage showcase, preferably around user location.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"success":true,"message":"Businesses retrieved successfully","timestamp":"2025-06-08T09:33:08.797638Z","data":[{"id":"01974ede-b71c-7156-a611-b2bddac2324c","tenant_id":"01974ede-b71b-7218-824e-fe395144eb45","business_name":"Quick Mart","business_type":"retail","subdomain":"royal-plaza","slug":"royal-plaza","description":"Your neighborhood convenience store for daily essentials and groceries.","logo_url":"https:\/\/via.placeholder.com\/200x200.png\/009955?text=business+exercitationem","contact_email":"<EMAIL>","contact_phone":"***********","status":"verified","cac_registration_number":"BN720413","tax_identification_number":"20692332-8156","global_auto_accept_orders":true,"auto_acceptance_criteria":{"min_order_value":1923,"max_order_value":45357,"verified_customers_only":false,"business_hours_only":true},"accepts_cash_on_delivery":true,"allows_pickup":true,"operating_hours":{"monday":{"open":"08:00","close":"22:00","is_closed":false},"tuesday":{"open":"08:00","close":"22:00","is_closed":false},"wednesday":{"open":"08:00","close":"22:00","is_closed":false},"thursday":{"open":"08:00","close":"22:00","is_closed":false},"friday":{"open":"08:00","close":"23:00","is_closed":false},"saturday":{"open":"09:00","close":"23:00","is_closed":false},"sunday":{"open":"10:00","close":"21:00","is_closed":false}},"verification_documents":{"cac_certificate":"http:\/\/www.torphy.com\/velit-consequatur-ut-quaerat-ratione-enim-aperiam-nostrum","tax_clearance":"https:\/\/www.kemmer.org\/perspiciatis-porro-harum-eos-id","business_permit":"http:\/\/www.quigley.com\/","owner_id":"http:\/\/www.marvin.com\/distinctio-pariatur-architecto-dignissimos","bank_statement":null},"country_id":"************************************","state_id":"01974ede-b0ce-703d-9207-2c7d7c045386","primary_address_id":"01974ede-b71e-70ea-8015-44fce2cc05d6","current_subscription_id":null,"created_at":"2025-06-08T09:28:28.000000Z","updated_at":"2025-06-08T09:28:28.000000Z","primary_address":{"id":"01974ede-b71e-70ea-8015-44fce2cc05d6","address_line_1":null,"address_line_2":null,"city":null,"state":null,"postal_code":null,"country":null,"latitude":"6.********","longitude":"3.********"},"country":{"id":"************************************","name":"Nigeria","code":"NG","currency_code":null,"phone_code":null},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"is_active":false,"is_verified":false,"has_primary_address":true,"has_logo":true,"has_subscription":false},{"id":"01974ede-b746-7256-a3d2-e55633512a76","tenant_id":"01974ede-b744-735c-9983-e62f19865452","business_name":"Style Central","business_type":"fashion","subdomain":"nigerian-shop","slug":"nigerian-shop","description":"Fashion-forward clothing and accessories for the style-conscious individual.","logo_url":null,"contact_email":"<EMAIL>","contact_phone":"***********","status":"verified","cac_registration_number":"RC767895","tax_identification_number":"01144705-5030","global_auto_accept_orders":false,"auto_acceptance_criteria":{"min_order_value":591,"max_order_value":39839,"verified_customers_only":false,"business_hours_only":true},"accepts_cash_on_delivery":true,"allows_pickup":true,"operating_hours":{"monday":{"open":"08:00","close":"22:00","is_closed":false},"tuesday":{"open":"08:00","close":"22:00","is_closed":false},"wednesday":{"open":"08:00","close":"22:00","is_closed":false},"thursday":{"open":"08:00","close":"22:00","is_closed":false},"friday":{"open":"08:00","close":"23:00","is_closed":false},"saturday":{"open":"09:00","close":"23:00","is_closed":false},"sunday":{"open":"10:00","close":"21:00","is_closed":true}},"verification_documents":{"cac_certificate":"http:\/\/www.russel.com\/praesentium-ut-quia-necessitatibus-velit-repudiandae","tax_clearance":"http:\/\/treutel.com\/","business_permit":null,"owner_id":"http:\/\/mertz.com\/","bank_statement":"http:\/\/kub.com\/deserunt-autem-et-harum"},"country_id":"************************************","state_id":"01974ede-b0ce-703d-9207-2c7d7c045386","primary_address_id":"01974ede-b747-70e9-83b6-042a31047cf3","current_subscription_id":null,"created_at":"2025-06-08T09:28:28.000000Z","updated_at":"2025-06-08T09:28:28.000000Z","primary_address":{"id":"01974ede-b747-70e9-83b6-042a31047cf3","address_line_1":null,"address_line_2":null,"city":null,"state":null,"postal_code":null,"country":null,"latitude":"6.********","longitude":"3.********"},"country":{"id":"************************************","name":"Nigeria","code":"NG","currency_code":null,"phone_code":null},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"is_active":false,"is_verified":false,"has_primary_address":true,"has_logo":false,"has_subscription":false},{"id":"01974ede-b6fd-7368-a742-4486bb56cd61","tenant_id":"01974ede-b6fc-7142-9869-94f98936e283","business_name":"Lagos Grill","business_type":"food","subdomain":"international-cafe","slug":"international-cafe","description":"Modern Nigerian cuisine with a contemporary twist. Grilled specialties and local favorites.","logo_url":"https:\/\/via.placeholder.com\/200x200.png\/007766?text=business+hic","contact_email":"<EMAIL>","contact_phone":"***********","status":"verified","cac_registration_number":"BN782839","tax_identification_number":"56621081-2128","global_auto_accept_orders":true,"auto_acceptance_criteria":{"min_order_value":1117,"max_order_value":23235,"verified_customers_only":false,"business_hours_only":false},"accepts_cash_on_delivery":true,"allows_pickup":true,"operating_hours":{"monday":{"open":"08:00","close":"22:00","is_closed":false},"tuesday":{"open":"08:00","close":"22:00","is_closed":false},"wednesday":{"open":"08:00","close":"22:00","is_closed":false},"thursday":{"open":"08:00","close":"22:00","is_closed":false},"friday":{"open":"08:00","close":"23:00","is_closed":false},"saturday":{"open":"09:00","close":"23:00","is_closed":false},"sunday":{"open":"10:00","close":"21:00","is_closed":false}},"verification_documents":{"cac_certificate":"http:\/\/www.klein.com\/","tax_clearance":"https:\/\/powlowski.com\/dolorem-ipsum-aut-aliquid.html","business_permit":"http:\/\/bailey.org\/quod-sint-dolores-libero-incidunt-porro","owner_id":"http:\/\/donnelly.com\/consectetur-consequatur-odio-velit-sint","bank_statement":null},"country_id":"************************************","state_id":"01974ede-b0ce-703d-9207-2c7d7c045386","primary_address_id":"01974ede-b6ff-7192-ba66-f2af4712ab43","current_subscription_id":null,"created_at":"2025-06-08T09:28:28.000000Z","updated_at":"2025-06-08T09:28:28.000000Z","primary_address":{"id":"01974ede-b6ff-7192-ba66-f2af4712ab43","address_line_1":null,"address_line_2":null,"city":null,"state":null,"postal_code":null,"country":null,"latitude":"6.********","longitude":"3.********"},"country":{"id":"************************************","name":"Nigeria","code":"NG","currency_code":null,"phone_code":null},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"is_active":false,"is_verified":false,"has_primary_address":true,"has_logo":true,"has_subscription":false},{"id":"01974ede-b742-73dd-9ce3-5bb0ce302040","tenant_id":"01974ede-b740-70d9-9e99-ca2120a9df3e","business_name":"Classy Wears","business_type":"fashion","subdomain":"continental-pharmacy","slug":"continental-pharmacy","description":"Elegant and sophisticated clothing for the discerning customer.","logo_url":"https:\/\/via.placeholder.com\/200x200.png\/002277?text=business+ut","contact_email":"<EMAIL>","contact_phone":"***********","status":"verified","cac_registration_number":"IT703887","tax_identification_number":"66502814-5858","global_auto_accept_orders":true,"auto_acceptance_criteria":{"min_order_value":1034,"max_order_value":25724,"verified_customers_only":false,"business_hours_only":true},"accepts_cash_on_delivery":true,"allows_pickup":true,"operating_hours":{"monday":{"open":"08:00","close":"22:00","is_closed":false},"tuesday":{"open":"08:00","close":"22:00","is_closed":false},"wednesday":{"open":"08:00","close":"22:00","is_closed":false},"thursday":{"open":"08:00","close":"22:00","is_closed":false},"friday":{"open":"08:00","close":"23:00","is_closed":false},"saturday":{"open":"09:00","close":"23:00","is_closed":false},"sunday":{"open":"10:00","close":"21:00","is_closed":true}},"verification_documents":{"cac_certificate":"http:\/\/www.rice.com\/vitae-illo-omnis-qui-ut-repudiandae-enim-vel","tax_clearance":null,"business_permit":null,"owner_id":"http:\/\/www.hettinger.biz\/","bank_statement":"http:\/\/bernhard.com\/"},"country_id":"************************************","state_id":"01974ede-b0ce-703d-9207-2c7d7c045386","primary_address_id":"01974ede-b743-70c0-8592-4395d71396b1","current_subscription_id":null,"created_at":"2025-06-08T09:28:28.000000Z","updated_at":"2025-06-08T09:28:28.000000Z","primary_address":{"id":"01974ede-b743-70c0-8592-4395d71396b1","address_line_1":null,"address_line_2":null,"city":null,"state":null,"postal_code":null,"country":null,"latitude":"6.********","longitude":"3.********"},"country":{"id":"************************************","name":"Nigeria","code":"NG","currency_code":null,"phone_code":null},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"is_active":false,"is_verified":false,"has_primary_address":true,"has_logo":true,"has_subscription":false},{"id":"01974ede-b72d-7326-8919-1a7083ef2348","tenant_id":"01974ede-b72b-7066-a86b-351e605546aa","business_name":"Mega Mart","business_type":"retail","subdomain":"elite-general-store","slug":"elite-general-store","description":"Large retail store with everything you need under one roof.","logo_url":null,"contact_email":"<EMAIL>","contact_phone":"***********","status":"verified","cac_registration_number":"IT275320","tax_identification_number":"96993205-1627","global_auto_accept_orders":false,"auto_acceptance_criteria":{"min_order_value":793,"max_order_value":18569,"verified_customers_only":false,"business_hours_only":true},"accepts_cash_on_delivery":false,"allows_pickup":true,"operating_hours":{"monday":{"open":"08:00","close":"22:00","is_closed":false},"tuesday":{"open":"08:00","close":"22:00","is_closed":false},"wednesday":{"open":"08:00","close":"22:00","is_closed":false},"thursday":{"open":"08:00","close":"22:00","is_closed":false},"friday":{"open":"08:00","close":"23:00","is_closed":false},"saturday":{"open":"09:00","close":"23:00","is_closed":false},"sunday":{"open":"10:00","close":"21:00","is_closed":false}},"verification_documents":{"cac_certificate":"https:\/\/www.nitzsche.net\/velit-totam-at-dolore-exercitationem-quo","tax_clearance":"http:\/\/king.com\/","business_permit":"http:\/\/www.fadel.com\/id-et-doloribus-ut-cum-nihil.html","owner_id":"http:\/\/www.huel.com\/","bank_statement":null},"country_id":"************************************","state_id":"01974ede-b0ce-703d-9207-2c7d7c045386","primary_address_id":"01974ede-b72e-7194-9f01-e0ae2bf003fb","current_subscription_id":null,"created_at":"2025-06-08T09:28:28.000000Z","updated_at":"2025-06-08T09:28:28.000000Z","primary_address":{"id":"01974ede-b72e-7194-9f01-e0ae2bf003fb","address_line_1":null,"address_line_2":null,"city":null,"state":null,"postal_code":null,"country":null,"latitude":"6.********","longitude":"3.********"},"country":{"id":"************************************","name":"Nigeria","code":"NG","currency_code":null,"phone_code":null},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"is_active":false,"is_verified":false,"has_primary_address":true,"has_logo":false,"has_subscription":false},{"id":"01974ede-b739-7157-b696-269936e614a6","tenant_id":"01974ede-b738-737a-b23e-46b220e4a30c","business_name":"Trendy Styles","business_type":"fashion","subdomain":"digital-grocery","slug":"digital-grocery","description":"Contemporary fashion store for the modern Nigerian woman and man.","logo_url":"https:\/\/via.placeholder.com\/200x200.png\/0033bb?text=business+accusamus","contact_email":"<EMAIL>","contact_phone":"***********","status":"verified","cac_registration_number":"BN443506","tax_identification_number":"26398901-3855","global_auto_accept_orders":false,"auto_acceptance_criteria":{"min_order_value":1620,"max_order_value":12477,"verified_customers_only":false,"business_hours_only":true},"accepts_cash_on_delivery":false,"allows_pickup":true,"operating_hours":{"monday":{"open":"08:00","close":"22:00","is_closed":false},"tuesday":{"open":"08:00","close":"22:00","is_closed":false},"wednesday":{"open":"08:00","close":"22:00","is_closed":false},"thursday":{"open":"08:00","close":"22:00","is_closed":false},"friday":{"open":"08:00","close":"23:00","is_closed":false},"saturday":{"open":"09:00","close":"23:00","is_closed":false},"sunday":{"open":"10:00","close":"21:00","is_closed":false}},"verification_documents":{"cac_certificate":"http:\/\/www.ratke.info\/perspiciatis-ut-voluptas-commodi-et-sunt-sequi-placeat","tax_clearance":null,"business_permit":null,"owner_id":"https:\/\/www.hamill.net\/numquam-dolorum-ab-dolores-praesentium","bank_statement":null},"country_id":"************************************","state_id":"01974ede-b0ce-703d-9207-2c7d7c045386","primary_address_id":"01974ede-b73b-70d0-8843-21442f65150a","current_subscription_id":null,"created_at":"2025-06-08T09:28:28.000000Z","updated_at":"2025-06-08T09:28:28.000000Z","primary_address":{"id":"01974ede-b73b-70d0-8843-21442f65150a","address_line_1":null,"address_line_2":null,"city":null,"state":null,"postal_code":null,"country":null,"latitude":"6.********","longitude":"3.********"},"country":{"id":"************************************","name":"Nigeria","code":"NG","currency_code":null,"phone_code":null},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"is_active":false,"is_verified":false,"has_primary_address":true,"has_logo":true,"has_subscription":false}],"request_id":"6621ef2b-5acc-441e-a3b1-917cb8b672e8"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 6621ef2b-5acc-441e-a3b1-917cb8b672e8
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-Qh5yV8YDs29xI6avstVgOA==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/showcase/featured-providers
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get random delivery providers for homepage showcase, preferably around user location.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"success":true,"message":"Delivery providers retrieved successfully","timestamp":"2025-06-08T09:33:08.810174Z","data":[{"id":"01974ede-b814-7047-b35d-2206dc707b5f","tenant_id":"01974ede-b80f-73ba-bb65-c4e592bedaad","company_name":"Swift Delivery Nigeria","description":"Fast and reliable delivery service across Nigeria","contact_email":"<EMAIL>","contact_phone":"08012345700","status":"verified","is_internal_provider":false,"performance_rating_avg":3.62,"country":{"id":"************************************","name":"Nigeria","code":"NG"},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"created_at":"2025-06-08T09:28:28.000000Z","updated_at":"2025-06-08T09:28:28.000000Z"},{"id":"01974ede-dd5f-7308-a5ae-1b8e1f7d720f","tenant_id":"01974ede-dd5a-73bc-bdfb-3245f46611f4","company_name":"Kano Fast Courier","description":"Northern Nigeria delivery specialist","contact_email":"<EMAIL>","contact_phone":"08012345704","status":"verified","is_internal_provider":false,"performance_rating_avg":4.7,"country":{"id":"************************************","name":"Nigeria","code":"NG"},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"created_at":"2025-06-08T09:28:37.000000Z","updated_at":"2025-06-08T09:28:37.000000Z"},{"id":"01974ede-e596-7012-a745-fa676ac378cb","tenant_id":"01974ede-b6de-714d-b0ee-74369293ab03","company_name":"Mama Cass Kitchen Delivery","description":"Internal delivery service for Mama Cass Kitchen","contact_email":"<EMAIL>","contact_phone":"***********","status":"active","is_internal_provider":true,"performance_rating_avg":4.85,"country":{"id":"************************************","name":"Nigeria","code":"NG"},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"created_at":"2025-06-08T09:28:40.000000Z","updated_at":"2025-06-08T09:28:40.000000Z"},{"id":"01974ede-e962-7239-bd85-f7026ac9a863","tenant_id":"01974ede-b6f3-71b6-b384-3e63383964f8","company_name":"Suya King Delivery","description":"Internal delivery service for Suya King","contact_email":"<EMAIL>","contact_phone":"08108242344","status":"active","is_internal_provider":true,"performance_rating_avg":4.35,"country":{"id":"************************************","name":"Nigeria","code":"NG"},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"created_at":"2025-06-08T09:28:41.000000Z","updated_at":"2025-06-08T09:28:41.000000Z"},{"id":"01974ede-d4d6-7154-b3db-790f8615cae6","tenant_id":"01974ede-d4d2-7098-a12e-5984c8e3080f","company_name":"Port Harcourt Delivery Hub","description":"Reliable delivery solutions for Rivers State","contact_email":"<EMAIL>","contact_phone":"***********","status":"verified","is_internal_provider":false,"performance_rating_avg":4.01,"country":{"id":"************************************","name":"Nigeria","code":"NG"},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"created_at":"2025-06-08T09:28:35.000000Z","updated_at":"2025-06-08T09:28:35.000000Z"},{"id":"01974ede-cc51-7311-8dbd-8e404e192a89","tenant_id":"01974ede-cc4c-73ac-91b4-a2020a89cf7c","company_name":"Abuja Quick Dispatch","description":"Same-day delivery service in Abuja and FCT","contact_email":"<EMAIL>","contact_phone":"08012345702","status":"verified","is_internal_provider":false,"performance_rating_avg":4.38,"country":{"id":"************************************","name":"Nigeria","code":"NG"},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"created_at":"2025-06-08T09:28:33.000000Z","updated_at":"2025-06-08T09:28:33.000000Z"}],"request_id":"5838a9f8-f986-49fb-b5d3-5ec3c6919937"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 5838a9f8-f986-49fb-b5d3-5ec3c6919937
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-NCjT7D0UMQXpmD4iRFVYAA==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/showcase/statistics
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get homepage statistics for showcase.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"success":true,"message":"Homepage statistics retrieved successfully","timestamp":"2025-06-08T09:33:08.817576Z","data":{"total_businesses":22,"total_providers":8,"total_orders_completed":0,"cities_served":1},"request_id":"ffc6bfab-44d5-4e08-bd84-a23b4e2fb05e"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: ffc6bfab-44d5-4e08-bd84-a23b4e2fb05e
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-OV9chfTClCqnO/AYrnX8WA==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/showcase/testimonials
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get recent success stories/testimonials for homepage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"success":true,"message":"Testimonials retrieved successfully","timestamp":"2025-06-08T09:33:08.821380Z","data":[{"id":1,"customer_name":"John Doe","business_name":"Pizza Palace","rating":5,"comment":"Amazing service and fast delivery!","created_at":"2025-06-06T09:33:08.821329Z"},{"id":2,"customer_name":"Jane Smith","business_name":"Quick Logistics","rating":5,"comment":"Professional delivery service, highly recommended!","created_at":"2025-06-03T09:33:08.821360Z"}],"request_id":"475fd3a4-f52a-4a9c-a066-f7e035605543"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 475fd3a4-f52a-4a9c-a066-f7e035605543
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-glIMRgmroMNkPULmihL/Ug==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/showcase
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get all homepage showcase data in one request.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"success":true,"message":"Homepage showcase data retrieved successfully","timestamp":"2025-06-08T09:33:08.831232Z","data":{"featured_businesses":[{"id":"01974ede-b6e3-728d-9cc5-4bc82548a9e2","tenant_id":"01974ede-b6de-714d-b0ee-74369293ab03","business_name":"Mama Cass Kitchen","business_type":"food","subdomain":"african-shop","slug":"african-shop","description":"Authentic Nigerian home-cooked meals with love and tradition. Specializing in local delicacies.","logo_url":null,"contact_email":"<EMAIL>","contact_phone":"***********","status":"verified","cac_registration_number":"BN451013","tax_identification_number":"87956478-3189","global_auto_accept_orders":true,"auto_acceptance_criteria":{"min_order_value":1850,"max_order_value":19424,"verified_customers_only":false,"business_hours_only":true},"accepts_cash_on_delivery":true,"allows_pickup":true,"operating_hours":{"monday":{"open":"08:00","close":"22:00","is_closed":false},"tuesday":{"open":"08:00","close":"22:00","is_closed":false},"wednesday":{"open":"08:00","close":"22:00","is_closed":false},"thursday":{"open":"08:00","close":"22:00","is_closed":false},"friday":{"open":"08:00","close":"23:00","is_closed":false},"saturday":{"open":"09:00","close":"23:00","is_closed":false},"sunday":{"open":"10:00","close":"21:00","is_closed":false}},"verification_documents":{"cac_certificate":null,"tax_clearance":null,"business_permit":"http:\/\/www.beahan.com\/","owner_id":"https:\/\/littel.com\/qui-tempore-quia-quo.html","bank_statement":"http:\/\/www.prosacco.info\/sequi-odio-ad-id-optio-quaerat-et-voluptatum-numquam.html"},"country_id":"************************************","state_id":"01974ede-b0ce-703d-9207-2c7d7c045386","primary_address_id":"01974ede-b6ec-73cd-8227-b00d9e07aa29","current_subscription_id":null,"created_at":"2025-06-08T09:28:28.000000Z","updated_at":"2025-06-08T09:28:28.000000Z","primary_address":{"id":"01974ede-b6ec-73cd-8227-b00d9e07aa29","address_line_1":null,"address_line_2":null,"city":null,"state":null,"postal_code":null,"country":null,"latitude":"6.********","longitude":"3.********"},"country":{"id":"************************************","name":"Nigeria","code":"NG","currency_code":null,"phone_code":null},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"is_active":false,"is_verified":false,"has_primary_address":true,"has_logo":false,"has_subscription":false},{"id":"01974ede-b74d-72d7-8001-8e41d8e56ef8","tenant_id":"01974ede-b74c-73d9-9a04-533e1534495c","business_name":"Home Care Solutions","business_type":"service","subdomain":"golden-electronics","slug":"golden-electronics","description":"Comprehensive home care and cleaning services for busy families.","logo_url":null,"contact_email":"<EMAIL>","contact_phone":"***********","status":"active","cac_registration_number":"BN441606","tax_identification_number":"87491098-6415","global_auto_accept_orders":true,"auto_acceptance_criteria":{"min_order_value":1150,"max_order_value":31255,"verified_customers_only":false,"business_hours_only":true},"accepts_cash_on_delivery":true,"allows_pickup":false,"operating_hours":{"monday":{"open":"08:00","close":"22:00","is_closed":false},"tuesday":{"open":"08:00","close":"22:00","is_closed":false},"wednesday":{"open":"08:00","close":"22:00","is_closed":false},"thursday":{"open":"08:00","close":"22:00","is_closed":false},"friday":{"open":"08:00","close":"23:00","is_closed":false},"saturday":{"open":"09:00","close":"23:00","is_closed":false},"sunday":{"open":"10:00","close":"21:00","is_closed":true}},"verification_documents":{"cac_certificate":"http:\/\/sawayn.com\/et-voluptatum-tenetur-voluptas-aut-impedit-ex-adipisci.html","tax_clearance":"http:\/\/www.nienow.com\/","business_permit":"https:\/\/www.roberts.com\/fuga-mollitia-ratione-tempora-ab","owner_id":null,"bank_statement":"http:\/\/mraz.com\/aperiam-qui-est-autem-neque-praesentium-vitae"},"country_id":"************************************","state_id":"01974ede-b0ce-703d-9207-2c7d7c045386","primary_address_id":"01974ede-b74f-7021-82f5-1402411941fc","current_subscription_id":null,"created_at":"2025-06-08T09:28:28.000000Z","updated_at":"2025-06-08T09:28:28.000000Z","primary_address":{"id":"01974ede-b74f-7021-82f5-1402411941fc","address_line_1":null,"address_line_2":null,"city":null,"state":null,"postal_code":null,"country":null,"latitude":"6.********","longitude":"3.********"},"country":{"id":"************************************","name":"Nigeria","code":"NG","currency_code":null,"phone_code":null},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"is_active":false,"is_verified":false,"has_primary_address":true,"has_logo":false,"has_subscription":false},{"id":"01974ede-b728-70f7-b9a3-a92d4292cd6a","tenant_id":"01974ede-b727-7178-8019-0ee1365e47e6","business_name":"Corner Shop","business_type":"retail","subdomain":"quick-supermarket","slug":"quick-supermarket","description":"Convenient corner store for quick shopping and everyday needs.","logo_url":"https:\/\/via.placeholder.com\/200x200.png\/00ee66?text=business+ea","contact_email":"<EMAIL>","contact_phone":"***********","status":"verified","cac_registration_number":"IT560985","tax_identification_number":"00766492-0400","global_auto_accept_orders":true,"auto_acceptance_criteria":{"min_order_value":1654,"max_order_value":26896,"verified_customers_only":true,"business_hours_only":true},"accepts_cash_on_delivery":true,"allows_pickup":true,"operating_hours":{"monday":{"open":"08:00","close":"22:00","is_closed":false},"tuesday":{"open":"08:00","close":"22:00","is_closed":false},"wednesday":{"open":"08:00","close":"22:00","is_closed":false},"thursday":{"open":"08:00","close":"22:00","is_closed":false},"friday":{"open":"08:00","close":"23:00","is_closed":false},"saturday":{"open":"09:00","close":"23:00","is_closed":false},"sunday":{"open":"10:00","close":"21:00","is_closed":false}},"verification_documents":{"cac_certificate":null,"tax_clearance":"https:\/\/www.sanford.com\/reiciendis-possimus-quam-quos-expedita-sit","business_permit":null,"owner_id":"http:\/\/yost.com\/qui-repudiandae-dolores-similique-officia-et-tenetur-quidem","bank_statement":null},"country_id":"************************************","state_id":"01974ede-b0ce-703d-9207-2c7d7c045386","primary_address_id":"01974ede-b72a-73b6-a070-921313da5130","current_subscription_id":null,"created_at":"2025-06-08T09:28:28.000000Z","updated_at":"2025-06-08T09:28:28.000000Z","primary_address":{"id":"01974ede-b72a-73b6-a070-921313da5130","address_line_1":null,"address_line_2":null,"city":null,"state":null,"postal_code":null,"country":null,"latitude":"6.********","longitude":"3.********"},"country":{"id":"************************************","name":"Nigeria","code":"NG","currency_code":null,"phone_code":null},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"is_active":false,"is_verified":false,"has_primary_address":true,"has_logo":true,"has_subscription":false},{"id":"01974ede-b742-73dd-9ce3-5bb0ce302040","tenant_id":"01974ede-b740-70d9-9e99-ca2120a9df3e","business_name":"Classy Wears","business_type":"fashion","subdomain":"continental-pharmacy","slug":"continental-pharmacy","description":"Elegant and sophisticated clothing for the discerning customer.","logo_url":"https:\/\/via.placeholder.com\/200x200.png\/002277?text=business+ut","contact_email":"<EMAIL>","contact_phone":"***********","status":"verified","cac_registration_number":"IT703887","tax_identification_number":"66502814-5858","global_auto_accept_orders":true,"auto_acceptance_criteria":{"min_order_value":1034,"max_order_value":25724,"verified_customers_only":false,"business_hours_only":true},"accepts_cash_on_delivery":true,"allows_pickup":true,"operating_hours":{"monday":{"open":"08:00","close":"22:00","is_closed":false},"tuesday":{"open":"08:00","close":"22:00","is_closed":false},"wednesday":{"open":"08:00","close":"22:00","is_closed":false},"thursday":{"open":"08:00","close":"22:00","is_closed":false},"friday":{"open":"08:00","close":"23:00","is_closed":false},"saturday":{"open":"09:00","close":"23:00","is_closed":false},"sunday":{"open":"10:00","close":"21:00","is_closed":true}},"verification_documents":{"cac_certificate":"http:\/\/www.rice.com\/vitae-illo-omnis-qui-ut-repudiandae-enim-vel","tax_clearance":null,"business_permit":null,"owner_id":"http:\/\/www.hettinger.biz\/","bank_statement":"http:\/\/bernhard.com\/"},"country_id":"************************************","state_id":"01974ede-b0ce-703d-9207-2c7d7c045386","primary_address_id":"01974ede-b743-70c0-8592-4395d71396b1","current_subscription_id":null,"created_at":"2025-06-08T09:28:28.000000Z","updated_at":"2025-06-08T09:28:28.000000Z","primary_address":{"id":"01974ede-b743-70c0-8592-4395d71396b1","address_line_1":null,"address_line_2":null,"city":null,"state":null,"postal_code":null,"country":null,"latitude":"6.********","longitude":"3.********"},"country":{"id":"************************************","name":"Nigeria","code":"NG","currency_code":null,"phone_code":null},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"is_active":false,"is_verified":false,"has_primary_address":true,"has_logo":true,"has_subscription":false},{"id":"01974ede-b6f8-7007-86c2-c5701044daad","tenant_id":"01974ede-b6f7-70ff-8a4c-ec2d1e124644","business_name":"Pepper Soup Joint","business_type":"food","subdomain":"premium-shop","slug":"premium-shop","description":"Traditional pepper soup restaurant serving catfish, goat meat, and chicken pepper soup.","logo_url":null,"contact_email":"<EMAIL>","contact_phone":"***********","status":"verified","cac_registration_number":"RC053519","tax_identification_number":"65125715-4711","global_auto_accept_orders":true,"auto_acceptance_criteria":{"min_order_value":1682,"max_order_value":16420,"verified_customers_only":false,"business_hours_only":false},"accepts_cash_on_delivery":true,"allows_pickup":true,"operating_hours":{"monday":{"open":"08:00","close":"22:00","is_closed":false},"tuesday":{"open":"08:00","close":"22:00","is_closed":false},"wednesday":{"open":"08:00","close":"22:00","is_closed":false},"thursday":{"open":"08:00","close":"22:00","is_closed":false},"friday":{"open":"08:00","close":"23:00","is_closed":false},"saturday":{"open":"09:00","close":"23:00","is_closed":false},"sunday":{"open":"10:00","close":"21:00","is_closed":false}},"verification_documents":{"cac_certificate":"http:\/\/www.mclaughlin.org\/eligendi-sit-numquam-aut-officia-dicta-ducimus","tax_clearance":null,"business_permit":"http:\/\/smitham.org\/quo-et-nobis-nobis","owner_id":"http:\/\/maggio.com\/atque-voluptas-similique-quia-expedita","bank_statement":"http:\/\/www.vandervort.com\/autem-temporibus-nesciunt-quo-nesciunt"},"country_id":"************************************","state_id":"01974ede-b0ce-703d-9207-2c7d7c045386","primary_address_id":"01974ede-b6fb-701a-8ef0-515684c7d83e","current_subscription_id":null,"created_at":"2025-06-08T09:28:28.000000Z","updated_at":"2025-06-08T09:28:28.000000Z","primary_address":{"id":"01974ede-b6fb-701a-8ef0-515684c7d83e","address_line_1":null,"address_line_2":null,"city":null,"state":null,"postal_code":null,"country":null,"latitude":"6.********","longitude":"3.********"},"country":{"id":"************************************","name":"Nigeria","code":"NG","currency_code":null,"phone_code":null},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"is_active":false,"is_verified":false,"has_primary_address":true,"has_logo":false,"has_subscription":false},{"id":"01974ede-b74a-722b-b13f-2f28c1d99cce","tenant_id":"01974ede-b748-7043-9d09-6246d95e0f20","business_name":"Quick Fix Services","business_type":"service","subdomain":"modern-restaurant","slug":"modern-restaurant","description":"Professional repair and maintenance services for home and office.","logo_url":"https:\/\/via.placeholder.com\/200x200.png\/004488?text=business+ab","contact_email":"<EMAIL>","contact_phone":"***********","status":"active","cac_registration_number":"BN684248","tax_identification_number":"53696733-0560","global_auto_accept_orders":false,"auto_acceptance_criteria":{"min_order_value":1616,"max_order_value":10195,"verified_customers_only":true,"business_hours_only":true},"accepts_cash_on_delivery":true,"allows_pickup":true,"operating_hours":{"monday":{"open":"08:00","close":"22:00","is_closed":false},"tuesday":{"open":"08:00","close":"22:00","is_closed":false},"wednesday":{"open":"08:00","close":"22:00","is_closed":false},"thursday":{"open":"08:00","close":"22:00","is_closed":false},"friday":{"open":"08:00","close":"23:00","is_closed":false},"saturday":{"open":"09:00","close":"23:00","is_closed":false},"sunday":{"open":"10:00","close":"21:00","is_closed":false}},"verification_documents":{"cac_certificate":"http:\/\/hilpert.com\/","tax_clearance":"http:\/\/olson.org\/aut-eius-nisi-qui-perferendis-repellendus.html","business_permit":null,"owner_id":"http:\/\/www.hand.org\/rem-aspernatur-et-ut-sit-laborum-odio-ratione","bank_statement":null},"country_id":"************************************","state_id":"01974ede-b0ce-703d-9207-2c7d7c045386","primary_address_id":"01974ede-b74b-7150-9e8e-dc48e339f6bb","current_subscription_id":null,"created_at":"2025-06-08T09:28:28.000000Z","updated_at":"2025-06-08T09:28:28.000000Z","primary_address":{"id":"01974ede-b74b-7150-9e8e-dc48e339f6bb","address_line_1":null,"address_line_2":null,"city":null,"state":null,"postal_code":null,"country":null,"latitude":"6.********","longitude":"3.********"},"country":{"id":"************************************","name":"Nigeria","code":"NG","currency_code":null,"phone_code":null},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"is_active":false,"is_verified":false,"has_primary_address":true,"has_logo":true,"has_subscription":false}],"featured_providers":[{"id":"01974ede-e596-7012-a745-fa676ac378cb","tenant_id":"01974ede-b6de-714d-b0ee-74369293ab03","company_name":"Mama Cass Kitchen Delivery","description":"Internal delivery service for Mama Cass Kitchen","contact_email":"<EMAIL>","contact_phone":"***********","status":"active","is_internal_provider":true,"performance_rating_avg":4.85,"country":{"id":"************************************","name":"Nigeria","code":"NG"},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"created_at":"2025-06-08T09:28:40.000000Z","updated_at":"2025-06-08T09:28:40.000000Z"},{"id":"01974ede-b814-7047-b35d-2206dc707b5f","tenant_id":"01974ede-b80f-73ba-bb65-c4e592bedaad","company_name":"Swift Delivery Nigeria","description":"Fast and reliable delivery service across Nigeria","contact_email":"<EMAIL>","contact_phone":"08012345700","status":"verified","is_internal_provider":false,"performance_rating_avg":3.62,"country":{"id":"************************************","name":"Nigeria","code":"NG"},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"created_at":"2025-06-08T09:28:28.000000Z","updated_at":"2025-06-08T09:28:28.000000Z"},{"id":"01974ede-dd5f-7308-a5ae-1b8e1f7d720f","tenant_id":"01974ede-dd5a-73bc-bdfb-3245f46611f4","company_name":"Kano Fast Courier","description":"Northern Nigeria delivery specialist","contact_email":"<EMAIL>","contact_phone":"08012345704","status":"verified","is_internal_provider":false,"performance_rating_avg":4.7,"country":{"id":"************************************","name":"Nigeria","code":"NG"},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"created_at":"2025-06-08T09:28:37.000000Z","updated_at":"2025-06-08T09:28:37.000000Z"},{"id":"01974ede-e7d9-7110-807b-051dc2ee18b3","tenant_id":"01974ede-b6ee-70c5-a8ec-07621304284f","company_name":"Jollof Palace Delivery","description":"Internal delivery service for Jollof Palace","contact_email":"<EMAIL>","contact_phone":"08091528086","status":"active","is_internal_provider":true,"performance_rating_avg":4.58,"country":{"id":"************************************","name":"Nigeria","code":"NG"},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"created_at":"2025-06-08T09:28:40.000000Z","updated_at":"2025-06-08T09:28:40.000000Z"},{"id":"01974ede-cc51-7311-8dbd-8e404e192a89","tenant_id":"01974ede-cc4c-73ac-91b4-a2020a89cf7c","company_name":"Abuja Quick Dispatch","description":"Same-day delivery service in Abuja and FCT","contact_email":"<EMAIL>","contact_phone":"08012345702","status":"verified","is_internal_provider":false,"performance_rating_avg":4.38,"country":{"id":"************************************","name":"Nigeria","code":"NG"},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"created_at":"2025-06-08T09:28:33.000000Z","updated_at":"2025-06-08T09:28:33.000000Z"},{"id":"01974ede-d4d6-7154-b3db-790f8615cae6","tenant_id":"01974ede-d4d2-7098-a12e-5984c8e3080f","company_name":"Port Harcourt Delivery Hub","description":"Reliable delivery solutions for Rivers State","contact_email":"<EMAIL>","contact_phone":"***********","status":"verified","is_internal_provider":false,"performance_rating_avg":4.01,"country":{"id":"************************************","name":"Nigeria","code":"NG"},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"created_at":"2025-06-08T09:28:35.000000Z","updated_at":"2025-06-08T09:28:35.000000Z"}],"statistics":{"total_businesses":22,"total_providers":8,"total_orders_completed":0,"cities_served":1},"testimonials":[{"id":1,"customer_name":"John Doe","business_name":"Pizza Palace","rating":5,"comment":"Amazing service and fast delivery!","created_at":"2025-06-06T09:33:08.831157Z"},{"id":2,"customer_name":"Jane Smith","business_name":"Quick Logistics","rating":5,"comment":"Professional delivery service, highly recommended!","created_at":"2025-06-03T09:33:08.831201Z"}]},"request_id":"072c869a-37ef-41e3-865c-4d043453cf6a"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 072c869a-37ef-41e3-865c-4d043453cf6a
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-3zn39FoKy0WnvcbJZKVLsQ==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/logout
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Logout user.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/logout-all
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Logout from all devices.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/refresh
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Refresh token.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/auth/me
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get authenticated user.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:08.839198Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 540240c2-b3b4-499d-9e89-4ca56fa3f8be
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-dp4rX8VrstR0k9LT0TiAqQ==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/email/send-verification
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Send email verification code.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/phone/send-verification
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Send phone verification code.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/email/verify
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Verify email with code.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      code:
        name: code
        description: 'The 6-digit verification code sent to your email. Must match the regex /^[0-9]{6}$/. Must be 6 characters.'
        required: true
        example: '123456'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      code: '123456'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/phone/verify
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Verify phone with code.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      code:
        name: code
        description: 'The 6-digit verification code sent to your phone. Must match the regex /^[0-9]{6}$/. Must be 6 characters.'
        required: true
        example: '123456'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      code: '123456'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/auth/tenants
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Get user's accessible tenants."
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:08.845102Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 0cc8c9ca-7268-4d9a-847e-2274c6e87ce7
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-8RIqU+XqkW6+2RXMltCaVg==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/auth/tenants/{tenantId}/access'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Check if user can interact with a specific tenant.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      tenantId:
        name: tenantId
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      tenantId: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:08.848648Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 23d3b3e3-a4d8-4eaa-9030-57579b1ec248
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-8LXPS8afOerd9fOwUhpHqQ==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/user/profile
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get user profile for central users only.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:08.858257Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: ad81b99c-f38f-435a-b90e-08aab908f0ec
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-9r9+nvPsHKI7UldJs8L1qA==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: api/v1/user/profile
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update user profile for central users only.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      first_name:
        name: first_name
        description: "User's first name. Must not be greater than 255 characters. Must be at least 2 characters."
        required: false
        example: John
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      last_name:
        name: last_name
        description: "User's last name. Must not be greater than 255 characters. Must be at least 2 characters."
        required: false
        example: Doe
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      email:
        name: email
        description: "User's email address. Must be a valid email address. Must not be greater than 255 characters."
        required: false
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      phone_number:
        name: phone_number
        description: 'User''s phone number in Nigerian format. Must match the regex /^\+234[0-9]{10}$/. Must not be greater than 20 characters.'
        required: false
        example: '+*************'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      timezone:
        name: timezone
        description: "User's timezone. Must be a valid time zone, such as <code>Africa/Accra</code>. Must not be greater than 50 characters."
        required: false
        example: Africa/Lagos
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      avatar_url:
        name: avatar_url
        description: "URL to user's avatar image. Must be a valid URL. Must not be greater than 500 characters."
        required: false
        example: 'https://example.com/avatar.jpg'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      bio:
        name: bio
        description: "User's biography. Must not be greater than 1000 characters."
        required: false
        example: 'Software developer passionate about technology'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      date_of_birth:
        name: date_of_birth
        description: "User's date of birth. Must be a valid date. Must be a date before <code>today</code>."
        required: false
        example: '1990-01-15'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      gender:
        name: gender
        description: "User's gender."
        required: false
        example: male
        type: string
        enumValues:
          - male
          - female
          - other
          - prefer_not_to_say
        exampleWasSpecified: false
        nullable: true
        custom: []
      address:
        name: address
        description: ''
        required: false
        example: null
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      address.street:
        name: address.street
        description: 'This field is required when <code>address</code> is present. Must not be greater than 255 characters.'
        required: false
        example: b
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      address.city_id:
        name: address.city_id
        description: 'This field is required when <code>address</code> is present. Must be a valid UUID. The <code>id</code> of an existing record in the cities table.'
        required: false
        example: a4855dc5-0acb-33c3-b921-f4291f719ca0
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      address.state_id:
        name: address.state_id
        description: 'This field is required when <code>address</code> is present. Must be a valid UUID. The <code>id</code> of an existing record in the states table.'
        required: false
        example: c90237e9-ced5-3af6-88ea-84aeaa148878
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      address.country_id:
        name: address.country_id
        description: 'This field is required when <code>address</code> is present. Must be a valid UUID. The <code>id</code> of an existing record in the countries table.'
        required: false
        example: a1a0a47d-e8c3-3cf0-8e6e-c1ff9dca5d1f
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      address.postal_code:
        name: address.postal_code
        description: 'Must not be greater than 20 characters.'
        required: false
        example: vdljnikhwaykcmyu
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      address.is_default:
        name: address.is_default
        description: ''
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      first_name: John
      last_name: Doe
      email: <EMAIL>
      phone_number: '+*************'
      timezone: Africa/Lagos
      avatar_url: 'https://example.com/avatar.jpg'
      bio: 'Software developer passionate about technology'
      date_of_birth: '1990-01-15'
      gender: male
      address:
        street: b
        city_id: a4855dc5-0acb-33c3-b921-f4291f719ca0
        state_id: c90237e9-ced5-3af6-88ea-84aeaa148878
        country_id: a1a0a47d-e8c3-3cf0-8e6e-c1ff9dca5d1f
        postal_code: vdljnikhwaykcmyu
        is_default: true
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/user/change-password
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Change user password for central users only.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      current_password:
        name: current_password
        description: 'Current password for verification.'
        required: true
        example: CurrentPassword123!
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      new_password:
        name: new_password
        description: 'New password (minimum 8 characters, must contain uppercase, lowercase, numbers, and symbols).'
        required: true
        example: NewSecurePassword123!
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      current_password: CurrentPassword123!
      new_password: NewSecurePassword123!
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/user/preferences
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get user preferences for central users only.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:08.867420Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: a23ea111-87b7-4c29-ac3b-94972fd60fef
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-q/JBfC12QBxoOEnzxAFUIw==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: api/v1/user/preferences
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update user preferences for central users only.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      notifications:
        name: notifications
        description: 'Notification preferences.'
        required: false
        example:
          email_enabled: true
          sms_enabled: false
          push_enabled: true
          marketing_enabled: false
          order_updates: true
          delivery_updates: true
          promotional_offers: false
          security_alerts: true
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notifications.email_enabled:
        name: notifications.email_enabled
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notifications.sms_enabled:
        name: notifications.sms_enabled
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notifications.push_enabled:
        name: notifications.push_enabled
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notifications.marketing_enabled:
        name: notifications.marketing_enabled
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notifications.order_updates:
        name: notifications.order_updates
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notifications.delivery_updates:
        name: notifications.delivery_updates
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notifications.promotional_offers:
        name: notifications.promotional_offers
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notifications.security_alerts:
        name: notifications.security_alerts
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      communication:
        name: communication
        description: 'Communication preferences.'
        required: false
        example:
          preferred_language: en
          preferred_contact_method: email
          contact_time_start: '09:00'
          contact_time_end: '18:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      communication.preferred_language:
        name: communication.preferred_language
        description: ''
        required: false
        example: en
        type: string
        enumValues:
          - en
          - yo
          - ig
          - ha
          - fr
        exampleWasSpecified: false
        nullable: false
        custom: []
      communication.preferred_contact_method:
        name: communication.preferred_contact_method
        description: ''
        required: false
        example: email
        type: string
        enumValues:
          - email
          - sms
          - phone
          - whatsapp
        exampleWasSpecified: false
        nullable: false
        custom: []
      communication.contact_time_start:
        name: communication.contact_time_start
        description: 'Must be a valid date in the format <code>H:i</code>.'
        required: false
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      communication.contact_time_end:
        name: communication.contact_time_end
        description: 'Must be a valid date in the format <code>H:i</code>.'
        required: false
        example: '18:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      privacy:
        name: privacy
        description: 'Privacy preferences.'
        required: false
        example:
          profile_visibility: private
          location_sharing: false
          activity_tracking: true
          data_analytics: true
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      privacy.profile_visibility:
        name: privacy.profile_visibility
        description: ''
        required: false
        example: private
        type: string
        enumValues:
          - public
          - private
          - contacts_only
        exampleWasSpecified: false
        nullable: false
        custom: []
      privacy.location_sharing:
        name: privacy.location_sharing
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      privacy.activity_tracking:
        name: privacy.activity_tracking
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      privacy.data_analytics:
        name: privacy.data_analytics
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      app:
        name: app
        description: 'Application preferences.'
        required: false
        example:
          theme: auto
          currency: NGN
          distance_unit: km
          temperature_unit: celsius
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      app.theme:
        name: app.theme
        description: ''
        required: false
        example: auto
        type: string
        enumValues:
          - light
          - dark
          - auto
        exampleWasSpecified: false
        nullable: false
        custom: []
      app.currency:
        name: app.currency
        description: ''
        required: false
        example: NGN
        type: string
        enumValues:
          - NGN
          - USD
          - EUR
          - GBP
        exampleWasSpecified: false
        nullable: false
        custom: []
      app.distance_unit:
        name: app.distance_unit
        description: ''
        required: false
        example: km
        type: string
        enumValues:
          - km
          - miles
        exampleWasSpecified: false
        nullable: false
        custom: []
      app.temperature_unit:
        name: app.temperature_unit
        description: ''
        required: false
        example: celsius
        type: string
        enumValues:
          - celsius
          - fahrenheit
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      notifications:
        email_enabled: true
        sms_enabled: false
        push_enabled: true
        marketing_enabled: false
        order_updates: true
        delivery_updates: true
        promotional_offers: false
        security_alerts: true
      communication:
        preferred_language: en
        preferred_contact_method: email
        contact_time_start: '09:00'
        contact_time_end: '18:00'
      privacy:
        profile_visibility: private
        location_sharing: false
        activity_tracking: true
        data_analytics: true
      app:
        theme: auto
        currency: NGN
        distance_unit: km
        temperature_unit: celsius
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/user/devices
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Get user's active devices/sessions."
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:08.877129Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: b040e3a0-cb61-482b-9b37-9e80b5326dee
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-ITFDNQJ0+/LZLFAR9ZsyJw==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/user/deactivate
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Deactivate user account for central users only.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      password:
        name: password
        description: 'Current password for verification. Must be at least 8 characters.'
        required: true
        example: password123
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      reason:
        name: reason
        description: 'Optional reason for account deactivation. Must not be greater than 500 characters.'
        required: false
        example: 'No longer needed'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      password: password123
      reason: 'No longer needed'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/notifications
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Get user's notifications with pagination and filtering."
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:08.885551Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: eccc1078-517d-4660-80de-420f58f87636
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-bieQ6IKsmBu/BbPm7G9QZA==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/notifications/stats
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get notification statistics.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:08.889481Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 18b54d94-80b1-4f11-bc97-20a8e70dddb3
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-njC+hMWY9duXeZBvtXeAvQ==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/notifications/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get specific notification.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the notification.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:08.893177Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 3c896030-811d-4394-a084-c7d801d71d30
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-yQjL4OL9smXkszJL+MfotA==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/v1/notifications/{id}/read'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Mark notification as read.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the notification.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: api/v1/notifications/mark-all-read
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Mark all notifications as read.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/v1/notifications/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Delete notification.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the notification.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/notifications/fcm-tokens
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Register FCM token for push notifications.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      token:
        name: token
        description: 'Must be at least 10 characters.'
        required: true
        example: bngzmiyvdljnikhwaykcmyuwpwlvqwrsitcpscqldzs
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      platform:
        name: platform
        description: ''
        required: true
        example: android
        type: string
        enumValues:
          - web
          - android
          - ios
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      token: bngzmiyvdljnikhwaykcmyuwpwlvqwrsitcpscqldzs
      platform: android
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/notifications/fcm-tokens
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Get user's FCM tokens."
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:08.899152Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: a9830ac7-ecf9-4146-8c90-173ceb9c3690
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-ja1dAFIHYVTx1seBfFpN+Q==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: api/v1/notifications/fcm-tokens
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Remove FCM token.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      token:
        name: token
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      token: architecto
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/notifications/preferences
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Get user's notification preferences."
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:08.902987Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: da64366e-54bc-4ed7-9a2a-61e778d6e98e
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-fXPsSJGWnv0jKHnP8Q2FxA==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: api/v1/notifications/preferences
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Update user's notification preferences."
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      channel:
        name: channel
        description: ''
        required: true
        example: push
        type: string
        enumValues:
          - push
          - sms
          - email
        exampleWasSpecified: false
        nullable: false
        custom: []
      type:
        name: type
        description: ''
        required: true
        example: payment_confirmation
        type: string
        enumValues:
          - order_status
          - delivery_update
          - payment_confirmation
          - promotional
          - system_announcement
        exampleWasSpecified: false
        nullable: false
        custom: []
      enabled:
        name: enabled
        description: ''
        required: true
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      channel: push
      type: payment_confirmation
      enabled: false
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/notifications/preferences/reset
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Reset notification preferences to defaults.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/kyc/verify
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Initiate comprehensive KYC verification.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      verification_type:
        name: verification_type
        description: ''
        required: true
        example: bvn
        type: string
        enumValues:
          - comprehensive
          - bvn
          - nin
          - bank_account
        exampleWasSpecified: false
        nullable: false
        custom: []
      bvn:
        name: bvn
        description: 'This field is required when <code>verification_type</code> is <code>comprehensive</code> or <code>bvn</code>. Must match the regex /^\d{11}$/.'
        required: false
        example: '***********'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      nin:
        name: nin
        description: 'This field is required when <code>verification_type</code> is <code>comprehensive</code> or <code>nin</code>. Must match the regex /^\d{11}$/.'
        required: false
        example: '***********'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      account_number:
        name: account_number
        description: 'This field is required when <code>verification_type</code> is <code>comprehensive</code> or <code>bank_account</code>. Must be at least 10 characters. Must not be greater than 10 characters.'
        required: false
        example: hwaykc
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      bank_code:
        name: bank_code
        description: 'This field is required when <code>verification_type</code> is <code>comprehensive</code> or <code>bank_account</code>. Must be at least 3 characters. Must not be greater than 6 characters.'
        required: false
        example: myuw
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      target_level:
        name: target_level
        description: ''
        required: false
        example: intermediate
        type: string
        enumValues:
          - basic
          - intermediate
          - advanced
        exampleWasSpecified: false
        nullable: false
        custom: []
      customer_data:
        name: customer_data
        description: ''
        required: false
        example: null
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      customer_data.first_name:
        name: customer_data.first_name
        description: 'Must not be greater than 255 characters.'
        required: false
        example: p
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      customer_data.last_name:
        name: customer_data.last_name
        description: 'Must not be greater than 255 characters.'
        required: false
        example: w
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      customer_data.phone_number:
        name: customer_data.phone_number
        description: 'Must not be greater than 20 characters.'
        required: false
        example: lvqwrsitcpscqldz
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      customer_data.date_of_birth:
        name: customer_data.date_of_birth
        description: 'Must be a valid date in the format <code>Y-m-d</code>.'
        required: false
        example: '2025-06-08'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      verification_type: bvn
      bvn: '***********'
      nin: '***********'
      account_number: hwaykc
      bank_code: myuw
      target_level: intermediate
      customer_data:
        first_name: p
        last_name: w
        phone_number: lvqwrsitcpscqldz
        date_of_birth: '2025-06-08'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/kyc/verify-component
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Verify individual component (BVN, NIN, Bank Account).'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      type:
        name: type
        description: ''
        required: true
        example: nin
        type: string
        enumValues:
          - bvn
          - nin
          - bank_account
        exampleWasSpecified: false
        nullable: false
        custom: []
      bvn:
        name: bvn
        description: 'This field is required when <code>type</code> is <code>bvn</code>. Must match the regex /^\d{11}$/.'
        required: false
        example: '***********'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      nin:
        name: nin
        description: 'This field is required when <code>type</code> is <code>nin</code>. Must match the regex /^\d{11}$/.'
        required: false
        example: '***********'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      account_number:
        name: account_number
        description: 'This field is required when <code>type</code> is <code>bank_account</code>. Must be at least 10 characters. Must not be greater than 10 characters.'
        required: false
        example: hwaykc
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      bank_code:
        name: bank_code
        description: 'This field is required when <code>type</code> is <code>bank_account</code>. Must be at least 3 characters. Must not be greater than 6 characters.'
        required: false
        example: myuw
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      customer_data:
        name: customer_data
        description: ''
        required: false
        example: null
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      customer_data.first_name:
        name: customer_data.first_name
        description: 'Must not be greater than 255 characters.'
        required: false
        example: p
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      customer_data.last_name:
        name: customer_data.last_name
        description: 'Must not be greater than 255 characters.'
        required: false
        example: w
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      customer_data.phone_number:
        name: customer_data.phone_number
        description: 'Must not be greater than 20 characters.'
        required: false
        example: lvqwrsitcpscqldz
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      customer_data.date_of_birth:
        name: customer_data.date_of_birth
        description: 'Must be a valid date in the format <code>Y-m-d</code>.'
        required: false
        example: '2025-06-08'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      type: nin
      bvn: '***********'
      nin: '***********'
      account_number: hwaykc
      bank_code: myuw
      customer_data:
        first_name: p
        last_name: w
        phone_number: lvqwrsitcpscqldz
        date_of_birth: '2025-06-08'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/kyc/status
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Get user's KYC status and progress."
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:08.934703Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: fd41462c-1a60-47ac-8582-2a6448c32512
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-QKBUAGBcKWtxL0vZ2KkyCg==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/kyc/report
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Generate comprehensive KYC report.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:08.938259Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 99062982-2518-442d-b532-cdcc1d62a87c
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-oZmxfZ46ScbR01toEpO4rA==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/kyc/methods
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get available verification methods.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:08.941355Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 0f838212-5746-4f77-a94b-008f3f91026e
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-0HlBPBRKEtkXMi57UpdxLg==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/kyc/banks
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get supported banks for verification.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:08.944401Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: edde6259-b452-47e9-87ac-************
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-5jsbrYQEacvVL8O+nOhvjA==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/kyc/pricing
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get verification pricing information.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:08.947540Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 7663a95f-9ddb-44ec-82e6-4328af7617b8
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-okaWFAxGJHDXYt07oPOnnQ==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/kyc/statistics
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get verification statistics.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:08.950952Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: ade26420-22e2-4fb4-9e59-282207f06851
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-qoBsjtS19SvuNbqvxiOmQw==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/register/admin
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Register platform admin (super admin only).'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      first_name:
        name: first_name
        description: 'First name of the admin user. Must be at least 2 characters. Must not be greater than 50 characters.'
        required: true
        example: John
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      last_name:
        name: last_name
        description: 'Last name of the admin user. Must be at least 2 characters. Must not be greater than 50 characters.'
        required: true
        example: Doe
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      email:
        name: email
        description: 'Email address for the admin account. Must be a valid email address.'
        required: true
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      password:
        name: password
        description: 'Password for the admin account (minimum 8 characters). Must be at least 8 characters.'
        required: true
        example: SecurePassword123!
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      phone_number:
        name: phone_number
        description: 'Phone number for the admin (optional). Must not be greater than 20 characters.'
        required: false
        example: '+*************'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      role:
        name: role
        description: 'Admin role - "platform-admin" or "super-admin".'
        required: true
        example: platform-admin
        type: string
        enumValues:
          - platform-admin
          - super-admin
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      first_name: John
      last_name: Doe
      email: <EMAIL>
      password: SecurePassword123!
      phone_number: '+*************'
      role: platform-admin
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/customer/businesses
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Browse businesses (customer discovery).'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:08.994611Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 034996a0-6ae3-48c2-936e-815a1bba3f73
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-GgezIRZb2gGAaRan7eaybg==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/customer/businesses/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Show business details for customers.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:08.998743Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 1e4e2234-95b8-48e6-a29a-fdcf37dcf6af
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-gw9Bcoh7GiVNkuMqTFyXpQ==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/customer/businesses/{id}/products'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get business products for customers.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:09.002310Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 642ce597-e138-4fc9-844e-917cdb5cab7e
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-pYrzsJ+6al+zx1PGK5Pd/A==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/customer/businesses/{id}/menu'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get business menu (organized by categories).'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:09.005722Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 5358f939-57cd-4d83-a254-28c279e43789
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-zsqGxl864s2WM6RsXd+Y+w==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/customer/providers
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Browse providers (customer discovery).'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:09.010065Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 88a14452-8d8d-4053-99c7-c912381d70bd
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-uOoNI0aE53KsHUva4kkENQ==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/customer/providers/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Show provider details for customers.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the provider.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:09.013679Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 93c69f23-7946-43b9-a94a-525061b66124
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-JUpUXdHUlgQ4ZO4TdB3CPA==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/customer/providers/{id}/service-areas'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get provider service areas for customers.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the provider.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:09.017205Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 7dca5b9d-f029-4315-9e40-4bc492a13f17
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-ty9xOSRulfDDQUThEUC7Jg==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/customer/providers/{id}/check-availability'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Check if provider serves a specific location.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the provider.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      latitude:
        name: latitude
        description: 'Must be between -90 and 90.'
        required: true
        example: -89
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      longitude:
        name: longitude
        description: 'Must be between -180 and 180.'
        required: true
        example: -179
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      latitude: -89
      longitude: -179
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/admin/tenants/statistics
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get tenant statistics for admin dashboard.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:09.083553Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 934ff665-e245-4b88-b38c-058c275877c5
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-LeOJPI/mmVQvSFMimpYCCw==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/admin/tenants/check-subdomain
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Check if subdomain is available.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:09.087969Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: ce295b79-ee10-470c-88e3-7cafdd076326
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-Zx+PZiYwmi2F2VYwiAwRtw==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/admin/tenants/{tenant}/restore'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Restore an archived tenant.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      tenant:
        name: tenant
        description: 'The tenant.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      tenant: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/v1/admin/tenants/{tenant}/status'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update tenant status.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      tenant:
        name: tenant
        description: 'The tenant.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      tenant: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      status:
        name: status
        description: 'New status for the tenant - "active", "inactive", "suspended", "pending_verification".'
        required: true
        example: active
        type: string
        enumValues:
          - active
          - pending
          - inactive
          - suspended
          - trial
          - expired
          - archived
        exampleWasSpecified: false
        nullable: false
        custom: []
      reason:
        name: reason
        description: 'Optional reason for the status change (max 500 characters). Must not be greater than 500 characters.'
        required: false
        example: 'Tenant has completed verification requirements'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      status: active
      reason: 'Tenant has completed verification requirements'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/admin/tenants
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Store a newly created tenant.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 'Name of the tenant (business or delivery provider). Must match the regex /^[a-zA-Z0-9\s\-_&.]+$/. Must be at least 2 characters. Must not be greater than 100 characters.'
        required: true
        example: 'Delicious Eats Restaurant'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      tenant_type:
        name: tenant_type
        description: 'Type of tenant - "business" or "delivery_provider".'
        required: true
        example: business
        type: string
        enumValues:
          - user
          - business
          - provider
          - all
        exampleWasSpecified: false
        nullable: false
        custom: []
      status:
        name: status
        description: 'Initial status of the tenant (optional, defaults to pending_verification).'
        required: false
        example: pending_verification
        type: string
        enumValues:
          - active
          - pending
          - inactive
          - suspended
          - trial
          - expired
          - archived
        exampleWasSpecified: false
        nullable: false
        custom: []
      subscription_plan_id:
        name: subscription_plan_id
        description: 'ID of the subscription plan to assign (optional). Must be a valid UUID. The <code>id</code> of an existing record in the subscription_plans table.'
        required: false
        example: a1b2c3d4-e5f6-7890-abcd-ef1234567890
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      subdomain:
        name: subdomain
        description: 'Custom subdomain for the tenant (optional, auto-generated if not provided). Must match the regex /^[a-z0-9-]+$/. Must be at least 3 characters. Must not be greater than 30 characters.'
        required: false
        example: delicious-eats
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      settings:
        name: settings
        description: 'Tenant settings configuration (optional).'
        required: false
        example:
          features:
            api_access_enabled: true
            webhook_enabled: false
            real_time_tracking: true
          limits:
            max_orders_per_month: 1000
            max_api_calls_per_day: 10000
            max_storage_mb: 1000
          security:
            two_factor_required: false
            session_timeout_minutes: 60
            password_expiry_days: 90
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      settings.features:
        name: settings.features
        description: ''
        required: false
        example:
          api_access_enabled: true
          webhook_enabled: false
          real_time_tracking: true
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.features.api_access_enabled:
        name: settings.features.api_access_enabled
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.features.webhook_enabled:
        name: settings.features.webhook_enabled
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.features.real_time_tracking:
        name: settings.features.real_time_tracking
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.limits:
        name: settings.limits
        description: ''
        required: false
        example:
          max_orders_per_month: 1000
          max_api_calls_per_day: 10000
          max_storage_mb: 1000
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.limits.max_orders_per_month:
        name: settings.limits.max_orders_per_month
        description: 'Must be at least 1. Must not be greater than 1000000.'
        required: false
        example: 1000
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.limits.max_api_calls_per_day:
        name: settings.limits.max_api_calls_per_day
        description: 'Must be at least 100. Must not be greater than 10000000.'
        required: false
        example: 10000
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.limits.max_storage_mb:
        name: settings.limits.max_storage_mb
        description: 'Must be at least 100. Must not be greater than 100000.'
        required: false
        example: 1000
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.security:
        name: settings.security
        description: ''
        required: false
        example:
          two_factor_required: false
          session_timeout_minutes: 60
          password_expiry_days: 90
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.security.two_factor_required:
        name: settings.security.two_factor_required
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.security.session_timeout_minutes:
        name: settings.security.session_timeout_minutes
        description: 'Must be at least 15. Must not be greater than 1440.'
        required: false
        example: 60
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.security.password_expiry_days:
        name: settings.security.password_expiry_days
        description: 'Must be at least 30. Must not be greater than 365.'
        required: false
        example: 90
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      name: 'Delicious Eats Restaurant'
      tenant_type: business
      status: pending_verification
      subscription_plan_id: a1b2c3d4-e5f6-7890-abcd-ef1234567890
      subdomain: delicious-eats
      settings:
        features:
          api_access_enabled: true
          webhook_enabled: false
          real_time_tracking: true
        limits:
          max_orders_per_month: 1000
          max_api_calls_per_day: 10000
          max_storage_mb: 1000
        security:
          two_factor_required: false
          session_timeout_minutes: 60
          password_expiry_days: 90
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/admin/tenants/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display the specified tenant.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the tenant.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:09.098682Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 212b5162-0abb-45e1-8164-a99d8b5865d8
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-7AQ3gZvdCckAgl5pqVH9mA==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
      - PATCH
    uri: 'api/v1/admin/tenants/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update the specified tenant.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the tenant.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 'Name of the tenant (business or delivery provider). Must match the regex /^[a-zA-Z0-9\s\-_&.]+$/. Must be at least 2 characters. Must not be greater than 100 characters.'
        required: false
        example: 'Delicious Eats Restaurant'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      tenant_type:
        name: tenant_type
        description: 'Type of tenant - "business" or "delivery_provider".'
        required: false
        example: business
        type: string
        enumValues:
          - user
          - business
          - provider
          - all
        exampleWasSpecified: false
        nullable: false
        custom: []
      status:
        name: status
        description: 'Status of the tenant.'
        required: false
        example: active
        type: string
        enumValues:
          - active
          - pending
          - inactive
          - suspended
          - trial
          - expired
          - archived
        exampleWasSpecified: false
        nullable: false
        custom: []
      subscription_plan_id:
        name: subscription_plan_id
        description: 'ID of the subscription plan to assign. Must be a valid UUID. The <code>id</code> of an existing record in the subscription_plans table.'
        required: false
        example: a1b2c3d4-e5f6-7890-abcd-ef1234567890
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      settings:
        name: settings
        description: 'Tenant settings configuration.'
        required: false
        example:
          features:
            api_access_enabled: true
            webhook_enabled: false
            real_time_tracking: true
          limits:
            max_orders_per_month: 1000
            max_api_calls_per_day: 10000
            max_storage_mb: 1000
          security:
            two_factor_required: false
            session_timeout_minutes: 60
            password_expiry_days: 90
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      settings.features:
        name: settings.features
        description: ''
        required: false
        example:
          api_access_enabled: true
          webhook_enabled: false
          real_time_tracking: true
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.features.api_access_enabled:
        name: settings.features.api_access_enabled
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.features.webhook_enabled:
        name: settings.features.webhook_enabled
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.features.real_time_tracking:
        name: settings.features.real_time_tracking
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.limits:
        name: settings.limits
        description: ''
        required: false
        example:
          max_orders_per_month: 1000
          max_api_calls_per_day: 10000
          max_storage_mb: 1000
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.limits.max_orders_per_month:
        name: settings.limits.max_orders_per_month
        description: 'Must be at least 1. Must not be greater than 1000000.'
        required: false
        example: 1000
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.limits.max_api_calls_per_day:
        name: settings.limits.max_api_calls_per_day
        description: 'Must be at least 100. Must not be greater than 10000000.'
        required: false
        example: 10000
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.limits.max_storage_mb:
        name: settings.limits.max_storage_mb
        description: 'Must be at least 100. Must not be greater than 100000.'
        required: false
        example: 1000
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.security:
        name: settings.security
        description: ''
        required: false
        example:
          two_factor_required: false
          session_timeout_minutes: 60
          password_expiry_days: 90
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.security.two_factor_required:
        name: settings.security.two_factor_required
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.security.session_timeout_minutes:
        name: settings.security.session_timeout_minutes
        description: 'Must be at least 15. Must not be greater than 1440.'
        required: false
        example: 60
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.security.password_expiry_days:
        name: settings.security.password_expiry_days
        description: 'Must be at least 30. Must not be greater than 365.'
        required: false
        example: 90
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      name: 'Delicious Eats Restaurant'
      tenant_type: business
      status: active
      subscription_plan_id: a1b2c3d4-e5f6-7890-abcd-ef1234567890
      settings:
        features:
          api_access_enabled: true
          webhook_enabled: false
          real_time_tracking: true
        limits:
          max_orders_per_month: 1000
          max_api_calls_per_day: 10000
          max_storage_mb: 1000
        security:
          two_factor_required: false
          session_timeout_minutes: 60
          password_expiry_days: 90
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/v1/admin/tenants/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Archive the specified tenant (soft delete).'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the tenant.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/admin/businesses
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display a listing of businesses.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:09.113059Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 89b63d16-7e06-4959-a3e2-cee35f69cdc3
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-UygSJynkytGG7dtYXtn3gQ==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/admin/businesses
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Store a newly created business.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      business_name:
        name: business_name
        description: 'Name of the business. Must not be greater than 255 characters. Must be at least 2 characters.'
        required: true
        example: 'Delicious Eats Restaurant'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      business_type:
        name: business_type
        description: 'Type of business.'
        required: true
        example: food
        type: string
        enumValues:
          - food
          - retail
          - fashion
          - service
          - other
        exampleWasSpecified: false
        nullable: false
        custom: []
      description:
        name: description
        description: 'Business description. Must not be greater than 1000 characters.'
        required: false
        example: 'A family-owned restaurant serving authentic Nigerian cuisine'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      subdomain:
        name: subdomain
        description: 'Unique subdomain for the business (auto-generated if not provided). Must match the regex /^[a-z0-9-]+$/. Must not be greater than 63 characters. Must be at least 3 characters.'
        required: false
        example: delicious-eats
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      slug:
        name: slug
        description: 'URL-friendly slug for the business (auto-generated if not provided). Must match the regex /^[a-z0-9-]+$/. Must not be greater than 255 characters.'
        required: false
        example: delicious-eats-restaurant
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      contact_email:
        name: contact_email
        description: 'Business contact email. Must be a valid email address. Must not be greater than 255 characters.'
        required: false
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      contact_phone:
        name: contact_phone
        description: 'Business contact phone number. Must not be greater than 20 characters.'
        required: false
        example: '+*************'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      country_id:
        name: country_id
        description: 'Country ID where the business is located. Must be a valid UUID. The <code>id</code> of an existing record in the countries table.'
        required: true
        example: a1b2c3d4-e5f6-7890-abcd-ef1234567890
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      state_id:
        name: state_id
        description: 'State ID where the business is located. Must be a valid UUID. The <code>id</code> of an existing record in the states table.'
        required: false
        example: b2c3d4e5-f6g7-8901-bcde-f23456789012
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      cac_registration_number:
        name: cac_registration_number
        description: 'Corporate Affairs Commission registration number. Must not be greater than 255 characters.'
        required: false
        example: RC123456
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      tax_identification_number:
        name: tax_identification_number
        description: 'Tax identification number. Must not be greater than 255 characters.'
        required: false
        example: TIN123456789
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      global_auto_accept_orders:
        name: global_auto_accept_orders
        description: 'Whether to automatically accept orders globally.'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      auto_acceptance_criteria:
        name: auto_acceptance_criteria
        description: 'Criteria for automatically accepting orders.'
        required: false
        example:
          min_value: 500
          max_distance: 5
          business_hours_only: true
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      auto_acceptance_criteria.min_value:
        name: auto_acceptance_criteria.min_value
        description: 'Must be at least 0.'
        required: false
        example: 500.0
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      auto_acceptance_criteria.max_distance:
        name: auto_acceptance_criteria.max_distance
        description: 'Must be at least 0.'
        required: false
        example: 5.0
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      auto_acceptance_criteria.business_hours_only:
        name: auto_acceptance_criteria.business_hours_only
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      accepts_cash_on_delivery:
        name: accepts_cash_on_delivery
        description: 'Whether the business accepts cash on delivery.'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      allows_pickup:
        name: allows_pickup
        description: 'Whether the business allows customer pickup.'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      operating_hours:
        name: operating_hours
        description: 'Business operating hours for each day.'
        required: false
        example:
          monday:
            open: '09:00'
            close: '22:00'
          tuesday:
            open: '09:00'
            close: '22:00'
          wednesday:
            open: '09:00'
            close: '22:00'
          thursday:
            open: '09:00'
            close: '22:00'
          friday:
            open: '09:00'
            close: '23:00'
          saturday:
            open: '10:00'
            close: '23:00'
          sunday:
            open: '12:00'
            close: '21:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.monday:
        name: operating_hours.monday
        description: ''
        required: false
        example:
          open: '09:00'
          close: '22:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.monday.open:
        name: operating_hours.monday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.monday.close:
        name: operating_hours.monday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '22:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.tuesday:
        name: operating_hours.tuesday
        description: ''
        required: false
        example:
          open: '09:00'
          close: '22:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.tuesday.open:
        name: operating_hours.tuesday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.tuesday.close:
        name: operating_hours.tuesday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '22:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.wednesday:
        name: operating_hours.wednesday
        description: ''
        required: false
        example:
          open: '09:00'
          close: '22:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.wednesday.open:
        name: operating_hours.wednesday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.wednesday.close:
        name: operating_hours.wednesday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '22:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.thursday:
        name: operating_hours.thursday
        description: ''
        required: false
        example:
          open: '09:00'
          close: '22:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.thursday.open:
        name: operating_hours.thursday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.thursday.close:
        name: operating_hours.thursday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '22:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.friday:
        name: operating_hours.friday
        description: ''
        required: false
        example:
          open: '09:00'
          close: '23:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.friday.open:
        name: operating_hours.friday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.friday.close:
        name: operating_hours.friday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '23:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.saturday:
        name: operating_hours.saturday
        description: ''
        required: false
        example:
          open: '10:00'
          close: '23:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.saturday.open:
        name: operating_hours.saturday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '10:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.saturday.close:
        name: operating_hours.saturday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '23:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.sunday:
        name: operating_hours.sunday
        description: ''
        required: false
        example:
          open: '12:00'
          close: '21:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.sunday.open:
        name: operating_hours.sunday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '12:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.sunday.close:
        name: operating_hours.sunday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '21:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      address:
        name: address
        description: 'Business address information.'
        required: false
        example:
          street: '123 Main Street'
          city: Lagos
          state: 'Lagos State'
          postal_code: '100001'
          country: Nigeria
          latitude: 6.5244
          longitude: 3.3792
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      address.address_line_1:
        name: address.address_line_1
        description: 'This field is required when <code>address</code> is present. Must not be greater than 255 characters.'
        required: false
        example: b
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      address.address_line_2:
        name: address.address_line_2
        description: 'Must not be greater than 255 characters.'
        required: false
        example: 'n'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      address.city:
        name: address.city
        description: 'This field is required when <code>address</code> is present. Must not be greater than 100 characters.'
        required: false
        example: Lagos
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      address.state:
        name: address.state
        description: 'This field is required when <code>address</code> is present. Must not be greater than 100 characters.'
        required: false
        example: 'Lagos State'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      address.postal_code:
        name: address.postal_code
        description: 'Must not be greater than 20 characters.'
        required: false
        example: '100001'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      address.latitude:
        name: address.latitude
        description: 'Must be between -90 and 90.'
        required: false
        example: 6.5244
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      address.longitude:
        name: address.longitude
        description: 'Must be between -180 and 180.'
        required: false
        example: 3.3792
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      admin_user_id:
        name: admin_user_id
        description: 'ID of the user to assign as business admin. Must be a valid UUID. The <code>id</code> of an existing record in the users table.'
        required: false
        example: c3d4e5f6-g7h8-9012-cdef-g34567890123
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      business_name: 'Delicious Eats Restaurant'
      business_type: food
      description: 'A family-owned restaurant serving authentic Nigerian cuisine'
      subdomain: delicious-eats
      slug: delicious-eats-restaurant
      contact_email: <EMAIL>
      contact_phone: '+*************'
      country_id: a1b2c3d4-e5f6-7890-abcd-ef1234567890
      state_id: b2c3d4e5-f6g7-8901-bcde-f23456789012
      cac_registration_number: RC123456
      tax_identification_number: TIN123456789
      global_auto_accept_orders: false
      auto_acceptance_criteria:
        min_value: 500
        max_distance: 5
        business_hours_only: true
      accepts_cash_on_delivery: false
      allows_pickup: false
      operating_hours:
        monday:
          open: '09:00'
          close: '22:00'
        tuesday:
          open: '09:00'
          close: '22:00'
        wednesday:
          open: '09:00'
          close: '22:00'
        thursday:
          open: '09:00'
          close: '22:00'
        friday:
          open: '09:00'
          close: '23:00'
        saturday:
          open: '10:00'
          close: '23:00'
        sunday:
          open: '12:00'
          close: '21:00'
      address:
        street: '123 Main Street'
        city: Lagos
        state: 'Lagos State'
        postal_code: '100001'
        country: Nigeria
        latitude: 6.5244
        longitude: 3.3792
        address_line_1: b
        address_line_2: 'n'
      admin_user_id: c3d4e5f6-g7h8-9012-cdef-g34567890123
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/admin/businesses/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display the specified business.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:09.124178Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: dbc2147e-adaa-4e95-98d7-554947d90fe9
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-xWk6tRlFTf2H10iUzyfZWQ==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
      - PATCH
    uri: 'api/v1/admin/businesses/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update the specified business.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      business_name:
        name: business_name
        description: 'Name of the business. Must not be greater than 255 characters. Must be at least 2 characters.'
        required: false
        example: 'Delicious Eats Restaurant'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      business_type:
        name: business_type
        description: 'Type of business.'
        required: false
        example: food
        type: string
        enumValues:
          - food
          - retail
          - fashion
          - service
          - other
        exampleWasSpecified: false
        nullable: false
        custom: []
      description:
        name: description
        description: 'Business description. Must not be greater than 1000 characters.'
        required: false
        example: 'A family-owned restaurant serving authentic Nigerian cuisine'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      subdomain:
        name: subdomain
        description: 'Unique subdomain for the business. Must match the regex /^[a-z0-9-]+$/. Must not be greater than 63 characters. Must be at least 3 characters.'
        required: false
        example: delicious-eats
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      slug:
        name: slug
        description: 'URL-friendly slug for the business. Must match the regex /^[a-z0-9-]+$/. Must not be greater than 255 characters.'
        required: false
        example: delicious-eats-restaurant
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      contact_email:
        name: contact_email
        description: 'Business contact email. Must be a valid email address. Must not be greater than 255 characters.'
        required: false
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      contact_phone:
        name: contact_phone
        description: 'Business contact phone number. Must not be greater than 20 characters.'
        required: false
        example: '+*************'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      country_id:
        name: country_id
        description: 'Country ID where the business is located. Must be a valid UUID. The <code>id</code> of an existing record in the countries table.'
        required: false
        example: a1b2c3d4-e5f6-7890-abcd-ef1234567890
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      state_id:
        name: state_id
        description: 'State ID where the business is located. Must be a valid UUID. The <code>id</code> of an existing record in the states table.'
        required: false
        example: b2c3d4e5-f6g7-8901-bcde-f23456789012
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      cac_registration_number:
        name: cac_registration_number
        description: 'Corporate Affairs Commission registration number. Must not be greater than 255 characters.'
        required: false
        example: RC123456
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      tax_identification_number:
        name: tax_identification_number
        description: 'Tax identification number. Must not be greater than 255 characters.'
        required: false
        example: TIN123456789
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      global_auto_accept_orders:
        name: global_auto_accept_orders
        description: 'Whether to automatically accept orders globally.'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      auto_acceptance_criteria:
        name: auto_acceptance_criteria
        description: 'Criteria for automatically accepting orders.'
        required: false
        example:
          min_value: 500
          max_distance: 5
          business_hours_only: true
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      auto_acceptance_criteria.min_value:
        name: auto_acceptance_criteria.min_value
        description: 'Must be at least 0.'
        required: false
        example: 500.0
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      auto_acceptance_criteria.max_distance:
        name: auto_acceptance_criteria.max_distance
        description: 'Must be at least 0.'
        required: false
        example: 5.0
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      auto_acceptance_criteria.business_hours_only:
        name: auto_acceptance_criteria.business_hours_only
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      accepts_cash_on_delivery:
        name: accepts_cash_on_delivery
        description: 'Whether the business accepts cash on delivery.'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      allows_pickup:
        name: allows_pickup
        description: 'Whether the business allows customer pickup.'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      operating_hours:
        name: operating_hours
        description: 'Business operating hours for each day.'
        required: false
        example:
          monday:
            open: '09:00'
            close: '22:00'
          tuesday:
            open: '09:00'
            close: '22:00'
          wednesday:
            open: '09:00'
            close: '22:00'
          thursday:
            open: '09:00'
            close: '22:00'
          friday:
            open: '09:00'
            close: '23:00'
          saturday:
            open: '10:00'
            close: '23:00'
          sunday:
            open: '12:00'
            close: '21:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.monday:
        name: operating_hours.monday
        description: ''
        required: false
        example:
          open: '09:00'
          close: '22:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.monday.open:
        name: operating_hours.monday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.monday.close:
        name: operating_hours.monday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '22:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.tuesday:
        name: operating_hours.tuesday
        description: ''
        required: false
        example:
          open: '09:00'
          close: '22:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.tuesday.open:
        name: operating_hours.tuesday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.tuesday.close:
        name: operating_hours.tuesday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '22:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.wednesday:
        name: operating_hours.wednesday
        description: ''
        required: false
        example:
          open: '09:00'
          close: '22:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.wednesday.open:
        name: operating_hours.wednesday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.wednesday.close:
        name: operating_hours.wednesday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '22:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.thursday:
        name: operating_hours.thursday
        description: ''
        required: false
        example:
          open: '09:00'
          close: '22:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.thursday.open:
        name: operating_hours.thursday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.thursday.close:
        name: operating_hours.thursday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '22:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.friday:
        name: operating_hours.friday
        description: ''
        required: false
        example:
          open: '09:00'
          close: '23:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.friday.open:
        name: operating_hours.friday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.friday.close:
        name: operating_hours.friday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '23:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.saturday:
        name: operating_hours.saturday
        description: ''
        required: false
        example:
          open: '10:00'
          close: '23:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.saturday.open:
        name: operating_hours.saturday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '10:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.saturday.close:
        name: operating_hours.saturday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '23:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.sunday:
        name: operating_hours.sunday
        description: ''
        required: false
        example:
          open: '12:00'
          close: '21:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.sunday.open:
        name: operating_hours.sunday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '12:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.sunday.close:
        name: operating_hours.sunday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '21:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      logo:
        name: logo
        description: 'Business logo image file (JPEG, PNG, JPG, GIF, or SVG, max 2MB). Must be an image. Must not be greater than 2048 kilobytes.'
        required: false
        example: null
        type: file
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      verification_documents:
        name: verification_documents
        description: 'Must not be greater than 500 characters.'
        required: false
        example:
          - b
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      business_name: 'Delicious Eats Restaurant'
      business_type: food
      description: 'A family-owned restaurant serving authentic Nigerian cuisine'
      subdomain: delicious-eats
      slug: delicious-eats-restaurant
      contact_email: <EMAIL>
      contact_phone: '+*************'
      country_id: a1b2c3d4-e5f6-7890-abcd-ef1234567890
      state_id: b2c3d4e5-f6g7-8901-bcde-f23456789012
      cac_registration_number: RC123456
      tax_identification_number: TIN123456789
      global_auto_accept_orders: false
      auto_acceptance_criteria:
        min_value: 500
        max_distance: 5
        business_hours_only: true
      accepts_cash_on_delivery: false
      allows_pickup: false
      operating_hours:
        monday:
          open: '09:00'
          close: '22:00'
        tuesday:
          open: '09:00'
          close: '22:00'
        wednesday:
          open: '09:00'
          close: '22:00'
        thursday:
          open: '09:00'
          close: '22:00'
        friday:
          open: '09:00'
          close: '23:00'
        saturday:
          open: '10:00'
          close: '23:00'
        sunday:
          open: '12:00'
          close: '21:00'
      verification_documents:
        - b
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/v1/admin/businesses/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Remove the specified business (soft delete).'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/admin/businesses/{business}/restore'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Restore an archived business.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      business:
        name: business
        description: 'The business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      business: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/admin/businesses/{business}/activate'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Activate a business.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      business:
        name: business
        description: 'The business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      business: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/admin/businesses/{business}/suspend'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Suspend a business.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      business:
        name: business
        description: 'The business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      business: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/admin/businesses/{business}/verify'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Mark business as verified.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      business:
        name: business
        description: 'The business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      business: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/admin/businesses/{business}/admins'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get business admins.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      business:
        name: business
        description: 'The business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      business: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:09.138228Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: ce1d1f43-ee24-45dd-b38a-e4a17a633e2b
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-CM6naqllKEBOPYHJlA97CA==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/admin/businesses/{business}/assign-admin'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Assign admin to business.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      business:
        name: business
        description: 'The business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      business: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      user_id:
        name: user_id
        description: 'Must be a valid UUID. The <code>id</code> of an existing record in the users table.'
        required: true
        example: 6ff8f7f6-1eb3-3525-be4a-3932c805afed
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      role:
        name: role
        description: ''
        required: true
        example: business-manager
        type: string
        enumValues:
          - business-admin
          - business-manager
          - business-owner
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      user_id: 6ff8f7f6-1eb3-3525-be4a-3932c805afed
      role: business-manager
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/admin/businesses/{business}/transfer-admin'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Transfer business ownership.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      business:
        name: business
        description: 'The business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      business: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      new_owner_id:
        name: new_owner_id
        description: 'Must be a valid UUID. The <code>id</code> of an existing record in the users table.'
        required: true
        example: 6ff8f7f6-1eb3-3525-be4a-3932c805afed
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      new_owner_id: 6ff8f7f6-1eb3-3525-be4a-3932c805afed
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/v1/admin/businesses/{business}/admins/{user}/role'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update admin role.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      business:
        name: business
        description: 'The business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      user:
        name: user
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      business: architecto
      user: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      role:
        name: role
        description: ''
        required: true
        example: business-admin
        type: string
        enumValues:
          - business-admin
          - business-manager
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      role: business-admin
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/v1/admin/businesses/{business}/admins/{user}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Remove admin from business.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      business:
        name: business
        description: 'The business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      user:
        name: user
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      business: architecto
      user: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/home
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: ''
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 500
        content: '{"success":false,"message":"Attempt to read property \"id\" on null","error_code":"INTERNAL_ERROR","timestamp":"2025-06-08T09:33:09.856102Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: a714ad3b-d12c-4354-bd60-5b085f09c91b
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-NPSZWUCh6WpZNA+LXq83rQ==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/tenant-auth/login
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Tenant domain login - Only allows users with matching tenant_id.'
      description: 'Used by business/provider staff on {tenant}.deliverynexus.com'
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      identifier:
        name: identifier
        description: "User's email address or phone number."
        required: true
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      password:
        name: password
        description: "User's password."
        required: true
        example: password123
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      remember_me:
        name: remember_me
        description: 'Whether to remember the user for extended login.'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      identifier: <EMAIL>
      password: password123
      remember_me: false
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/tenant-auth/logout
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Logout user.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/tenant-auth/refresh
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Refresh token.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/tenant-auth/me
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get authenticated user.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:09.867118Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 705aedde-2747-400b-b6ae-72ae53ce6a84
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-apbWeqr5/lkX0hmd26813A==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/provider/service-areas/{area}/activate'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Activate service area.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      area:
        name: area
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      area: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/provider/service-areas/{area}/deactivate'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Deactivate service area.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      area:
        name: area
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      area: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/tenant/user/profile
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get user profile for tenant users only.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:10.149724Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: b03a13ac-8051-43c5-9689-99ff26d9aea9
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-fMagcCnv6Xse+g9V5Us3Xg==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: api/v1/tenant/user/profile
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update user profile for tenant users only.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      first_name:
        name: first_name
        description: "User's first name. Must not be greater than 255 characters. Must be at least 2 characters."
        required: false
        example: John
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      last_name:
        name: last_name
        description: "User's last name. Must not be greater than 255 characters. Must be at least 2 characters."
        required: false
        example: Doe
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      email:
        name: email
        description: "User's email address. Must be a valid email address. Must not be greater than 255 characters."
        required: false
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      phone_number:
        name: phone_number
        description: 'User''s phone number in Nigerian format. Must match the regex /^\+234[0-9]{10}$/. Must not be greater than 20 characters.'
        required: false
        example: '+*************'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      timezone:
        name: timezone
        description: "User's timezone. Must be a valid time zone, such as <code>Africa/Accra</code>. Must not be greater than 50 characters."
        required: false
        example: Africa/Lagos
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      avatar_url:
        name: avatar_url
        description: "URL to user's avatar image. Must be a valid URL. Must not be greater than 500 characters."
        required: false
        example: 'https://example.com/avatar.jpg'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      bio:
        name: bio
        description: "User's biography. Must not be greater than 1000 characters."
        required: false
        example: 'Software developer passionate about technology'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      date_of_birth:
        name: date_of_birth
        description: "User's date of birth. Must be a valid date. Must be a date before <code>today</code>."
        required: false
        example: '1990-01-15'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      gender:
        name: gender
        description: "User's gender."
        required: false
        example: male
        type: string
        enumValues:
          - male
          - female
          - other
          - prefer_not_to_say
        exampleWasSpecified: false
        nullable: true
        custom: []
      address:
        name: address
        description: ''
        required: false
        example: null
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      address.street:
        name: address.street
        description: 'This field is required when <code>address</code> is present. Must not be greater than 255 characters.'
        required: false
        example: b
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      address.city_id:
        name: address.city_id
        description: 'This field is required when <code>address</code> is present. Must be a valid UUID. The <code>id</code> of an existing record in the cities table.'
        required: false
        example: a4855dc5-0acb-33c3-b921-f4291f719ca0
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      address.state_id:
        name: address.state_id
        description: 'This field is required when <code>address</code> is present. Must be a valid UUID. The <code>id</code> of an existing record in the states table.'
        required: false
        example: c90237e9-ced5-3af6-88ea-84aeaa148878
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      address.country_id:
        name: address.country_id
        description: 'This field is required when <code>address</code> is present. Must be a valid UUID. The <code>id</code> of an existing record in the countries table.'
        required: false
        example: a1a0a47d-e8c3-3cf0-8e6e-c1ff9dca5d1f
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      address.postal_code:
        name: address.postal_code
        description: 'Must not be greater than 20 characters.'
        required: false
        example: vdljnikhwaykcmyu
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      address.is_default:
        name: address.is_default
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      first_name: John
      last_name: Doe
      email: <EMAIL>
      phone_number: '+*************'
      timezone: Africa/Lagos
      avatar_url: 'https://example.com/avatar.jpg'
      bio: 'Software developer passionate about technology'
      date_of_birth: '1990-01-15'
      gender: male
      address:
        street: b
        city_id: a4855dc5-0acb-33c3-b921-f4291f719ca0
        state_id: c90237e9-ced5-3af6-88ea-84aeaa148878
        country_id: a1a0a47d-e8c3-3cf0-8e6e-c1ff9dca5d1f
        postal_code: vdljnikhwaykcmyu
        is_default: false
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/tenant/user/change-password
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Change user password for tenant users only.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      current_password:
        name: current_password
        description: 'Current password for verification.'
        required: true
        example: CurrentPassword123!
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      new_password:
        name: new_password
        description: 'New password (minimum 8 characters, must contain uppercase, lowercase, numbers, and symbols).'
        required: true
        example: NewSecurePassword123!
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      current_password: CurrentPassword123!
      new_password: NewSecurePassword123!
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/tenant/user/preferences
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get user preferences for tenant users only.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:10.158286Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 790b98c9-6db6-4c32-b8f3-3c69da0c0032
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-YZobYgiv/PxQrA3SAEIcbA==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: api/v1/tenant/user/preferences
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update user preferences for tenant users only.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      notifications:
        name: notifications
        description: 'Notification preferences.'
        required: false
        example:
          email_enabled: true
          sms_enabled: false
          push_enabled: true
          marketing_enabled: false
          order_updates: true
          delivery_updates: true
          promotional_offers: false
          security_alerts: true
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notifications.email_enabled:
        name: notifications.email_enabled
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notifications.sms_enabled:
        name: notifications.sms_enabled
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notifications.push_enabled:
        name: notifications.push_enabled
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notifications.marketing_enabled:
        name: notifications.marketing_enabled
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notifications.order_updates:
        name: notifications.order_updates
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notifications.delivery_updates:
        name: notifications.delivery_updates
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notifications.promotional_offers:
        name: notifications.promotional_offers
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notifications.security_alerts:
        name: notifications.security_alerts
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      communication:
        name: communication
        description: 'Communication preferences.'
        required: false
        example:
          preferred_language: en
          preferred_contact_method: email
          contact_time_start: '09:00'
          contact_time_end: '18:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      communication.preferred_language:
        name: communication.preferred_language
        description: ''
        required: false
        example: en
        type: string
        enumValues:
          - en
          - yo
          - ig
          - ha
          - fr
        exampleWasSpecified: false
        nullable: false
        custom: []
      communication.preferred_contact_method:
        name: communication.preferred_contact_method
        description: ''
        required: false
        example: email
        type: string
        enumValues:
          - email
          - sms
          - phone
          - whatsapp
        exampleWasSpecified: false
        nullable: false
        custom: []
      communication.contact_time_start:
        name: communication.contact_time_start
        description: 'Must be a valid date in the format <code>H:i</code>.'
        required: false
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      communication.contact_time_end:
        name: communication.contact_time_end
        description: 'Must be a valid date in the format <code>H:i</code>.'
        required: false
        example: '18:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      privacy:
        name: privacy
        description: 'Privacy preferences.'
        required: false
        example:
          profile_visibility: private
          location_sharing: false
          activity_tracking: true
          data_analytics: true
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      privacy.profile_visibility:
        name: privacy.profile_visibility
        description: ''
        required: false
        example: private
        type: string
        enumValues:
          - public
          - private
          - contacts_only
        exampleWasSpecified: false
        nullable: false
        custom: []
      privacy.location_sharing:
        name: privacy.location_sharing
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      privacy.activity_tracking:
        name: privacy.activity_tracking
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      privacy.data_analytics:
        name: privacy.data_analytics
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      app:
        name: app
        description: 'Application preferences.'
        required: false
        example:
          theme: auto
          currency: NGN
          distance_unit: km
          temperature_unit: celsius
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      app.theme:
        name: app.theme
        description: ''
        required: false
        example: auto
        type: string
        enumValues:
          - light
          - dark
          - auto
        exampleWasSpecified: false
        nullable: false
        custom: []
      app.currency:
        name: app.currency
        description: ''
        required: false
        example: NGN
        type: string
        enumValues:
          - NGN
          - USD
          - EUR
          - GBP
        exampleWasSpecified: false
        nullable: false
        custom: []
      app.distance_unit:
        name: app.distance_unit
        description: ''
        required: false
        example: km
        type: string
        enumValues:
          - km
          - miles
        exampleWasSpecified: false
        nullable: false
        custom: []
      app.temperature_unit:
        name: app.temperature_unit
        description: ''
        required: false
        example: celsius
        type: string
        enumValues:
          - celsius
          - fahrenheit
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      notifications:
        email_enabled: true
        sms_enabled: false
        push_enabled: true
        marketing_enabled: false
        order_updates: true
        delivery_updates: true
        promotional_offers: false
        security_alerts: true
      communication:
        preferred_language: en
        preferred_contact_method: email
        contact_time_start: '09:00'
        contact_time_end: '18:00'
      privacy:
        profile_visibility: private
        location_sharing: false
        activity_tracking: true
        data_analytics: true
      app:
        theme: auto
        currency: NGN
        distance_unit: km
        temperature_unit: celsius
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/tenant/user/deactivate
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Deactivate user account for tenant users only.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      password:
        name: password
        description: 'Current password for verification. Must be at least 8 characters.'
        required: true
        example: password123
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      reason:
        name: reason
        description: 'Optional reason for account deactivation. Must not be greater than 500 characters.'
        required: false
        example: 'No longer needed'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      password: password123
      reason: 'No longer needed'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
