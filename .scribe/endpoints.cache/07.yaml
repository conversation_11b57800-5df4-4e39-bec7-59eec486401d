## Autogenerated by <PERSON>ribe. DO NOT MODIFY.

name: 'Central Customer - Unified Search'
description: ''
endpoints:
  -
    httpMethods:
      - POST
    uri: api/v1/customer/search
    metadata:
      groupName: 'Central Customer - Unified Search'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Multi-domain search across all entities.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      query:
        name: query
        description: 'Search query.'
        required: true
        example: restaurant
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      context:
        name: context
        description: 'Search context (business, product, delivery_provider, all). Default: all.'
        required: false
        example: all
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      sort:
        name: sort
        description: 'Sort results (relevance, name_asc, name_desc, newest, oldest).'
        required: false
        example: relevance
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'Results per page (max 50).'
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      page:
        name: page
        description: 'Page number.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      query: restaurant
      context: all
      sort: relevance
      per_page: 15
      page: 1
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Search results retrieved successfully",
            "data": {
              "query": "restaurant",
              "total_results": 25,
              "categories": {
                "businesses": {
                  "data": [...],
                  "total": 15,
                  "has_more": true
                },
                "products": {
                  "data": [...],
                  "total": 8,
                  "has_more": false
                },
                "delivery_providers": {
                  "data": [...],
                  "total": 2,
                  "has_more": false
                }
              },
              "pagination": {
                "current_page": 1,
                "per_page": 15
              }
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/customer/search/suggestions
    metadata:
      groupName: 'Central Customer - Unified Search'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get search suggestions across all domains.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      query:
        name: query
        description: 'Search query for suggestions.'
        required: true
        example: rest
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      context:
        name: context
        description: 'Search context (business, product, delivery_provider, all). Default: all.'
        required: false
        example: all
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      limit:
        name: limit
        description: 'Number of suggestions per category (max 10).'
        required: false
        example: 5
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      query: rest
      context: all
      limit: 5
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Search suggestions retrieved successfully",
            "data": {
              "businesses": ["Restaurant ABC", "Restaurant XYZ"],
              "products": ["Rest API Book", "Restaurant Menu"],
              "delivery_providers": ["RestFast Delivery"]
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/customer/search/trending
    metadata:
      groupName: 'Central Customer - Unified Search'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get trending search terms across all domains.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      limit:
        name: limit
        description: 'Number of trending terms (max 20).'
        required: false
        example: 10
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      limit: 10
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Trending search terms retrieved successfully",
            "data": {
              "trending_terms": ["pizza", "burger", "sushi", "delivery"],
              "popular_categories": ["restaurants", "groceries", "pharmacy"]
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
