## Autogenerated by <PERSON>ribe. DO NOT MODIFY.

name: 'Customer Payments'
description: ''
endpoints:
  -
    httpMethods:
      - POST
    uri: 'api/v1/customer/orders/{order}/payment/initialize'
    metadata:
      groupName: 'Customer Payments'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Initialize payment for an order.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      order:
        name: order
        description: 'Order ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      order: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      callback_url:
        name: callback_url
        description: 'Optional callback URL after payment.'
        required: false
        example: 'https://app.deliverynexus.com/payment/callback'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      callback_url: 'https://app.deliverynexus.com/payment/callback'
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Payment initialized successfully",
            "data": {
              "payment_id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "reference": "PAY_DN123456_ABC12345",
              "authorization_url": "https://checkout.paystack.com/abc123",
              "access_code": "abc123def456",
              "amount": 15750.00,
              "currency": "NGN"
            }
          }
        headers: []
        description: ''
        custom: []
      -
        status: 422
        content: |-
          {
            "success": false,
            "message": "Order payment has already been processed",
            "error_code": "PAYMENT_ALREADY_PROCESSED"
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/customer/payments/verify
    metadata:
      groupName: 'Customer Payments'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Verify payment completion.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      reference:
        name: reference
        description: 'Payment reference to verify.'
        required: true
        example: PAY_DN123456_ABC12345
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      reference: PAY_DN123456_ABC12345
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Payment verified successfully",
            "data": {
              "verified": true,
              "payment_status": "paid",
              "order_id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "amount": 15750.00,
              "currency": "NGN"
            }
          }
        headers: []
        description: ''
        custom: []
      -
        status: 422
        content: |-
          {
            "success": false,
            "message": "Payment verification failed",
            "data": {
              "verified": false,
              "payment_status": "failed"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/customer/orders/{order}/payment-methods'
    metadata:
      groupName: 'Customer Payments'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get available payment methods for an order.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      order:
        name: order
        description: 'Order ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      order: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Payment methods retrieved successfully",
            "data": [
              {
                "type": "card",
                "name": "Debit/Credit Card",
                "description": "Pay securely with your debit or credit card",
                "icon": "credit-card",
                "enabled": true
              },
              {
                "type": "bank_transfer",
                "name": "Bank Transfer",
                "description": "Pay directly from your bank account",
                "icon": "bank",
                "enabled": true
              },
              {
                "type": "cash_on_delivery",
                "name": "Cash on Delivery",
                "description": "Pay with cash when your order is delivered",
                "icon": "cash",
                "enabled": true
              }
            ]
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/customer/payments/history
    metadata:
      groupName: 'Customer Payments'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get payment history for the customer.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      status:
        name: status
        description: 'Filter by payment status (pending, paid, failed, refunded).'
        required: false
        example: paid
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      order_id:
        name: order_id
        description: 'Filter by specific order ID.'
        required: false
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'Items per page (1-100).'
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      status: paid
      order_id: 019723aa-3202-70dd-a0c1-3565681dd87a
      per_page: 15
    bodyParameters:
      status:
        name: status
        description: ''
        required: false
        example: paid
        type: string
        enumValues:
          - pending
          - paid
          - failed
          - refunded
          - partially_refunded
        exampleWasSpecified: false
        nullable: false
        custom: []
      order_id:
        name: order_id
        description: 'Must be a valid UUID. The <code>id</code> of an existing record in the orders table.'
        required: false
        example: 6ff8f7f6-1eb3-3525-be4a-3932c805afed
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'Must be at least 1. Must not be greater than 100.'
        required: false
        example: 7
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      status: paid
      order_id: 6ff8f7f6-1eb3-3525-be4a-3932c805afed
      per_page: 7
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Payment history retrieved successfully",
            "data": [
              {
                "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                "order_id": "019723aa-3202-70dd-a0c1-3565681dd87b",
                "order_reference": "DN123456",
                "amount": 15750.00,
                "currency": "NGN",
                "status": "paid",
                "gateway": "paystack",
                "gateway_reference": "PAY_DN123456_ABC12345",
                "paid_at": "2024-01-22T15:30:00Z",
                "created_at": "2024-01-22T15:25:00Z"
              }
            ],
            "meta": {
              "current_page": 1,
              "per_page": 15,
              "total": 25,
              "last_page": 2
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
