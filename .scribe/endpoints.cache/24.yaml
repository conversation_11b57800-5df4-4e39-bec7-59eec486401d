## Autogenerated by Scribe. DO NOT MODIFY.

name: 'Admin Analytics'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1/admin/analytics/overview
    metadata:
      groupName: 'Admin Analytics'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get platform overview analytics.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      period:
        name: period
        description: 'Period for analytics (today, week, month, quarter, year).'
        required: false
        example: month
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      timezone:
        name: timezone
        description: 'Timezone for date calculations.'
        required: false
        example: Africa/Lagos
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      period: month
      timezone: Africa/Lagos
    bodyParameters:
      period:
        name: period
        description: ''
        required: false
        example: quarter
        type: string
        enumValues:
          - today
          - week
          - month
          - quarter
          - year
        exampleWasSpecified: false
        nullable: false
        custom: []
      timezone:
        name: timezone
        description: ''
        required: false
        example: Asia/Yekaterinburg
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      period: quarter
      timezone: Asia/Yekaterinburg
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Platform analytics retrieved successfully",
            "data": {
              "overview": {
                "total_businesses": 150,
                "total_providers": 75,
                "total_users": 2500,
                "total_orders": 15000,
                "total_deliveries": 12000,
                "platform_revenue": 2500000,
                "active_subscriptions": 180
              },
              "period_stats": {
                "period": "month",
                "new_businesses": 12,
                "new_providers": 8,
                "new_users": 450,
                "orders_count": 2500,
                "deliveries_count": 2100,
                "revenue": 450000
              },
              "growth_metrics": {
                "business_growth": 8.5,
                "provider_growth": 12.3,
                "user_growth": 22.1,
                "revenue_growth": 15.7
              }
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/admin/analytics/real-time-dashboard
    metadata:
      groupName: 'Admin Analytics'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get real-time dashboard data.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:09.189975Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 27d6e6af-bb4e-4e72-8cb3-fb8c0ccce598
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-Q9VNVVp1QoHrQeZQnJnxFA==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/admin/analytics/business-performance
    metadata:
      groupName: 'Admin Analytics'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get business performance analytics.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      date_from:
        name: date_from
        description: 'Must be a valid date.'
        required: false
        example: '2025-06-08T09:33:09'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      date_to:
        name: date_to
        description: 'Must be a valid date. Must be a date after or equal to <code>date_from</code>.'
        required: false
        example: '2051-07-02'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      date_from: '2025-06-08T09:33:09'
      date_to: '2051-07-02'
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:09.196014Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: e7507ebe-8a7e-42a5-aadd-346c88fcf9d0
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-kRs/gVn9eTHUyi8NKj6Hdw==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/admin/analytics/revenue-analytics
    metadata:
      groupName: 'Admin Analytics'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get revenue analytics.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      date_from:
        name: date_from
        description: 'Must be a valid date.'
        required: false
        example: '2025-06-08T09:33:09'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      date_to:
        name: date_to
        description: 'Must be a valid date. Must be a date after or equal to <code>date_from</code>.'
        required: false
        example: '2051-07-02'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      date_from: '2025-06-08T09:33:09'
      date_to: '2051-07-02'
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:09.201844Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 0e236320-9214-4181-a064-077b3a968be5
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-FQY7JQRAN6KD3yENBv64SQ==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/admin/analytics/user-engagement
    metadata:
      groupName: 'Admin Analytics'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get user engagement analytics.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      date_from:
        name: date_from
        description: 'Must be a valid date.'
        required: false
        example: '2025-06-08T09:33:09'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      date_to:
        name: date_to
        description: 'Must be a valid date. Must be a date after or equal to <code>date_from</code>.'
        required: false
        example: '2051-07-02'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      date_from: '2025-06-08T09:33:09'
      date_to: '2051-07-02'
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:09.207980Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 6b968b71-af11-4857-aed1-f826d509f907
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-AU744A77AnDIA0x/UUXxaw==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/admin/analytics/operational-metrics
    metadata:
      groupName: 'Admin Analytics'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get operational metrics.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      date_from:
        name: date_from
        description: 'Must be a valid date.'
        required: false
        example: '2025-06-08T09:33:09'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      date_to:
        name: date_to
        description: 'Must be a valid date. Must be a date after or equal to <code>date_from</code>.'
        required: false
        example: '2051-07-02'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      date_from: '2025-06-08T09:33:09'
      date_to: '2051-07-02'
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:09.213073Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: f5447080-2941-4340-8d49-ea7f3f9a70db
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-5z9uzaPVV5C482/w7kKF+A==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/admin/analytics/subscriptions
    metadata:
      groupName: 'Admin Analytics'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get subscription analytics.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      period:
        name: period
        description: ''
        required: false
        example: week
        type: string
        enumValues:
          - today
          - week
          - month
          - quarter
          - year
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      period: week
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:09.217172Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 8fc6f5e9-aa4d-4288-9350-49e960d47084
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-M1yElu/40pp0TcvM0GLM4g==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/admin/analytics/financial
    metadata:
      groupName: 'Admin Analytics'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get financial analytics.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      period:
        name: period
        description: ''
        required: false
        example: week
        type: string
        enumValues:
          - today
          - week
          - month
          - quarter
          - year
        exampleWasSpecified: false
        nullable: false
        custom: []
      currency:
        name: currency
        description: ''
        required: false
        example: NGN
        type: string
        enumValues:
          - NGN
          - USD
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      period: week
      currency: NGN
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:09.221817Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 89360501-4ef8-4937-8491-649cbe984408
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-z+sUEaKn3LdqZYFbDYil3A==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/admin/analytics/usage
    metadata:
      groupName: 'Admin Analytics'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get usage analytics.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      period:
        name: period
        description: ''
        required: false
        example: year
        type: string
        enumValues:
          - today
          - week
          - month
          - quarter
          - year
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      period: year
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:09.226912Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: e1dd6a46-e6ae-4e35-a2ff-51ede385cb95
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-AlwdAQauQ/RMkaPycNJlCA==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/admin/analytics/custom-report
    metadata:
      groupName: 'Admin Analytics'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Generate custom report.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      type:
        name: type
        description: ''
        required: true
        example: business_performance
        type: string
        enumValues:
          - business_performance
          - revenue_analysis
          - user_engagement
          - operational_overview
          - platform_overview
        exampleWasSpecified: false
        nullable: false
        custom: []
      date_from:
        name: date_from
        description: 'Must be a valid date.'
        required: false
        example: '2025-06-08T09:33:09'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      date_to:
        name: date_to
        description: 'Must be a valid date. Must be a date after or equal to <code>date_from</code>.'
        required: false
        example: '2051-07-02'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      filters:
        name: filters
        description: ''
        required: false
        example: null
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      type: business_performance
      date_from: '2025-06-08T09:33:09'
      date_to: '2051-07-02'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/admin/analytics/export
    metadata:
      groupName: 'Admin Analytics'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Export analytics data.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      type:
        name: type
        description: ''
        required: true
        example: financial
        type: string
        enumValues:
          - overview
          - subscriptions
          - financial
          - usage
        exampleWasSpecified: false
        nullable: false
        custom: []
      period:
        name: period
        description: ''
        required: false
        example: week
        type: string
        enumValues:
          - today
          - week
          - month
          - quarter
          - year
        exampleWasSpecified: false
        nullable: false
        custom: []
      format:
        name: format
        description: ''
        required: false
        example: pdf
        type: string
        enumValues:
          - csv
          - xlsx
          - pdf
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      type: financial
      period: week
      format: pdf
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/admin/analytics/download/{filename}'
    metadata:
      groupName: 'Admin Analytics'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Download exported analytics data.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      filename:
        name: filename
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      filename: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:09.233954Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 36a53c4a-fae6-492a-a82b-d15e58c00588
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-MZqlSPR7ni9EuvB6Wl6Tfg==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
