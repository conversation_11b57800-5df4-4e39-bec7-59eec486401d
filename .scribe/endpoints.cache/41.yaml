## Autogenerated by Scribe. DO NOT MODIFY.

name: 'Admin Plans'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1/admin/plans
    metadata:
      groupName: 'Admin Plans'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get all subscription plans.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:09.597053Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 4343e34b-3d5c-40f5-be3f-fc79465922c9
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-Pic9BwD+772MtN0RI8VR0w==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/admin/plans
    metadata:
      groupName: 'Admin Plans'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Create new subscription plan.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 'Must not be greater than 255 characters.'
        required: true
        example: b
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      slug:
        name: slug
        description: 'Must not be greater than 255 characters.'
        required: true
        example: 'n'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      description:
        name: description
        description: 'Must not be greater than 1000 characters.'
        required: true
        example: 'Animi quos velit et fugiat.'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      target_type:
        name: target_type
        description: ''
        required: true
        example: architecto
        type: string
        enumValues:
          - user
          - business
          - provider
          - all
        exampleWasSpecified: false
        nullable: false
        custom: []
      is_active:
        name: is_active
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      prices:
        name: prices
        description: 'Must have at least 1 items.'
        required: false
        example: null
        type: 'object[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      features:
        name: features
        description: ''
        required: false
        example: null
        type: 'object[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'prices[].billing_interval':
        name: 'prices[].billing_interval'
        description: 'This field is required when <code>prices</code> is present.'
        required: false
        example: monthly
        type: string
        enumValues:
          - monthly
          - quarterly
          - annually
        exampleWasSpecified: false
        nullable: false
        custom: []
      'prices[].price':
        name: 'prices[].price'
        description: 'This field is required when <code>prices</code> is present. Must be at least 0.'
        required: false
        example: 39
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'prices[].currency':
        name: 'prices[].currency'
        description: 'This field is required when <code>prices</code> is present.'
        required: false
        example: NGN
        type: string
        enumValues:
          - NGN
          - USD
        exampleWasSpecified: false
        nullable: false
        custom: []
      'prices[].is_active':
        name: 'prices[].is_active'
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'features[].feature_id':
        name: 'features[].feature_id'
        description: 'This field is required when <code>features</code> is present. Must be a valid UUID. The <code>id</code> of an existing record in the features table.'
        required: false
        example: 6b72fe4a-5b40-307c-bc24-f79acf9a1bb9
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'features[].limit':
        name: 'features[].limit'
        description: 'Must be at least 0.'
        required: false
        example: 77
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      'features[].is_enabled':
        name: 'features[].is_enabled'
        description: 'This field is required when <code>features</code> is present.'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      name: b
      slug: 'n'
      description: 'Animi quos velit et fugiat.'
      target_type: architecto
      is_active: false
      prices:
        -
          billing_interval: monthly
          price: 39
          currency: NGN
          is_active: false
      features:
        -
          feature_id: 6b72fe4a-5b40-307c-bc24-f79acf9a1bb9
          limit: 77
          is_enabled: false
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/admin/plans/{plan}'
    metadata:
      groupName: 'Admin Plans'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get specific subscription plan.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      plan:
        name: plan
        description: 'The plan.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      plan: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:09.602508Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: f30cb5b0-43c3-45ec-a4c3-a4b444b7277f
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-Kuht5vNCsyBkudF7piuH9g==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/v1/admin/plans/{plan}'
    metadata:
      groupName: 'Admin Plans'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update subscription plan.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      plan:
        name: plan
        description: 'The plan.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      plan: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 'Must not be greater than 255 characters.'
        required: false
        example: b
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      slug:
        name: slug
        description: 'Must not be greater than 255 characters.'
        required: false
        example: 'n'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      description:
        name: description
        description: 'Must not be greater than 1000 characters.'
        required: false
        example: 'Animi quos velit et fugiat.'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      target_type:
        name: target_type
        description: ''
        required: false
        example: architecto
        type: string
        enumValues:
          - user
          - business
          - provider
          - all
        exampleWasSpecified: false
        nullable: false
        custom: []
      is_active:
        name: is_active
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      prices:
        name: prices
        description: 'Must have at least 1 items.'
        required: false
        example: null
        type: 'object[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      features:
        name: features
        description: ''
        required: false
        example: null
        type: 'object[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'prices[].billing_interval':
        name: 'prices[].billing_interval'
        description: 'This field is required when <code>prices</code> is present.'
        required: false
        example: annually
        type: string
        enumValues:
          - monthly
          - quarterly
          - annually
        exampleWasSpecified: false
        nullable: false
        custom: []
      'prices[].price':
        name: 'prices[].price'
        description: 'This field is required when <code>prices</code> is present. Must be at least 0.'
        required: false
        example: 39
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'prices[].currency':
        name: 'prices[].currency'
        description: 'This field is required when <code>prices</code> is present.'
        required: false
        example: USD
        type: string
        enumValues:
          - NGN
          - USD
        exampleWasSpecified: false
        nullable: false
        custom: []
      'prices[].is_active':
        name: 'prices[].is_active'
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'features[].feature_id':
        name: 'features[].feature_id'
        description: 'This field is required when <code>features</code> is present. Must be a valid UUID. The <code>id</code> of an existing record in the features table.'
        required: false
        example: 6b72fe4a-5b40-307c-bc24-f79acf9a1bb9
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'features[].limit':
        name: 'features[].limit'
        description: 'Must be at least 0.'
        required: false
        example: 77
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      'features[].is_enabled':
        name: 'features[].is_enabled'
        description: 'This field is required when <code>features</code> is present.'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      name: b
      slug: 'n'
      description: 'Animi quos velit et fugiat.'
      target_type: architecto
      is_active: false
      prices:
        -
          billing_interval: annually
          price: 39
          currency: USD
          is_active: false
      features:
        -
          feature_id: 6b72fe4a-5b40-307c-bc24-f79acf9a1bb9
          limit: 77
          is_enabled: false
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/v1/admin/plans/{plan}'
    metadata:
      groupName: 'Admin Plans'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Delete subscription plan.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      plan:
        name: plan
        description: 'The plan.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      plan: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/v1/admin/plans/{plan}/status'
    metadata:
      groupName: 'Admin Plans'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Toggle plan active status.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      plan:
        name: plan
        description: 'The plan.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      plan: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      is_active:
        name: is_active
        description: ''
        required: true
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      is_active: false
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/v1/admin/plans/{plan}/pricing'
    metadata:
      groupName: 'Admin Plans'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Manage plan pricing.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      plan:
        name: plan
        description: 'The plan.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      plan: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      prices:
        name: prices
        description: ''
        required: true
        example:
          - []
        type: 'object[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'prices[].billing_interval':
        name: 'prices[].billing_interval'
        description: ''
        required: true
        example: quarterly
        type: string
        enumValues:
          - monthly
          - quarterly
          - annually
        exampleWasSpecified: false
        nullable: false
        custom: []
      'prices[].price':
        name: 'prices[].price'
        description: 'Must be at least 0.'
        required: true
        example: 27
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'prices[].currency':
        name: 'prices[].currency'
        description: ''
        required: true
        example: USD
        type: string
        enumValues:
          - NGN
          - USD
        exampleWasSpecified: false
        nullable: false
        custom: []
      'prices[].is_active':
        name: 'prices[].is_active'
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      prices:
        -
          billing_interval: quarterly
          price: 27
          currency: USD
          is_active: false
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/v1/admin/plans/{plan}/features'
    metadata:
      groupName: 'Admin Plans'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Manage plan features.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      plan:
        name: plan
        description: 'The plan.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      plan: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      features:
        name: features
        description: ''
        required: true
        example:
          - []
        type: 'object[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'features[].feature_id':
        name: 'features[].feature_id'
        description: 'Must be a valid UUID. The <code>id</code> of an existing record in the features table.'
        required: true
        example: 6ff8f7f6-1eb3-3525-be4a-3932c805afed
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'features[].limit':
        name: 'features[].limit'
        description: 'Must be at least 0.'
        required: false
        example: 84
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      'features[].is_enabled':
        name: 'features[].is_enabled'
        description: ''
        required: true
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      features:
        -
          feature_id: 6ff8f7f6-1eb3-3525-be4a-3932c805afed
          limit: 84
          is_enabled: false
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/admin/plans/{plan}/analytics'
    metadata:
      groupName: 'Admin Plans'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get plan analytics.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      plan:
        name: plan
        description: 'The plan.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      plan: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:09.611345Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 07006c05-c3a3-4c9d-aef8-1cbc6c099d7d
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-yMdXDUci3Ff+v0Jqrk4S4Q==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/admin/plans/{plan}/clone'
    metadata:
      groupName: 'Admin Plans'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Clone existing plan.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      plan:
        name: plan
        description: 'The plan.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      plan: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 'Must not be greater than 255 characters.'
        required: true
        example: b
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      slug:
        name: slug
        description: 'Must not be greater than 255 characters.'
        required: true
        example: 'n'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      name: b
      slug: 'n'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
