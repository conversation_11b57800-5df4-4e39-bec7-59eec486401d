## Autogenerated by Scribe. DO NOT MODIFY.

name: 'Business Categories'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1/business/categories
    metadata:
      groupName: 'Business Categories'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display a listing of categories for the business.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      page:
        name: page
        description: 'Page number for pagination.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'Number of items per page (max 100).'
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      search:
        name: search
        description: 'Search in category name, description.'
        required: false
        example: Main
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      parent_id:
        name: parent_id
        description: 'Filter by parent category ID.'
        required: false
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      is_active:
        name: is_active
        description: 'Filter by active status.'
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      with_products:
        name: with_products
        description: 'Filter categories that have products.'
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      tree:
        name: tree
        description: 'Return hierarchical tree structure.'
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      sort_by:
        name: sort_by
        description: 'Sort by field (name, sort_order, created_at).'
        required: false
        example: sort_order
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      sort_direction:
        name: sort_direction
        description: 'Sort direction (asc, desc).'
        required: false
        example: asc
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      page: 1
      per_page: 15
      search: Main
      parent_id: 019723aa-3202-70dd-a0c1-3565681dd87a
      is_active: true
      with_products: true
      tree: true
      sort_by: sort_order
      sort_direction: asc
    bodyParameters:
      page:
        name: page
        description: 'Must be at least 1.'
        required: false
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'Must be at least 1. Must not be greater than 100.'
        required: false
        example: 22
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      search:
        name: search
        description: 'Must not be greater than 255 characters.'
        required: false
        example: g
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      parent_id:
        name: parent_id
        description: 'The <code>id</code> of an existing record in the product_categories table.'
        required: false
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      is_active:
        name: is_active
        description: ''
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      with_products:
        name: with_products
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      tree:
        name: tree
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      sort_by:
        name: sort_by
        description: ''
        required: false
        example: display_order
        type: string
        enumValues:
          - name
          - display_order
          - created_at
        exampleWasSpecified: false
        nullable: false
        custom: []
      sort_direction:
        name: sort_direction
        description: ''
        required: false
        example: desc
        type: string
        enumValues:
          - asc
          - desc
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      page: 16
      per_page: 22
      search: g
      parent_id: architecto
      is_active: true
      with_products: false
      tree: false
      sort_by: display_order
      sort_direction: desc
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Categories retrieved successfully",
            "data": {
              "categories": [
                {
                  "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                  "name": "Main Dishes",
                  "slug": "main-dishes",
                  "description": "Primary food items",
                  "image_url": "https://example.com/main-dishes.jpg",
                  "is_active": true,
                  "sort_order": 1,
                  "is_root": true,
                  "has_children": true,
                  "has_products": true,
                  "products_count": 15,
                  "children_count": 3,
                  "depth": 0,
                  "full_path": "Main Dishes",
                  "parent": null,
                  "children": [],
                  "created_at": "2024-01-15T10:30:00Z"
                }
              ],
              "pagination": {
                "current_page": 1,
                "per_page": 15,
                "total": 10,
                "last_page": 1
              }
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/business/categories
    metadata:
      groupName: 'Business Categories'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Store a newly created category.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 'Category name.'
        required: true
        example: 'Main Dishes'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      description:
        name: description
        description: 'Category description.'
        required: false
        example: 'Primary food items'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      parent_id:
        name: parent_id
        description: 'Parent category ID for subcategories.'
        required: false
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      image_url:
        name: image_url
        description: 'Category image URL.'
        required: false
        example: 'https://example.com/main-dishes.jpg'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      is_active:
        name: is_active
        description: 'Category active status.'
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      display_order:
        name: display_order
        description: 'Must be at least 0.'
        required: false
        example: 77
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      sort_order:
        name: sort_order
        description: 'Sort order for display.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      attributes:
        name: attributes
        description: 'Category attributes.'
        required: false
        example:
          color: '#ff0000'
          icon: utensils
        type: object
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      name: 'Main Dishes'
      description: 'Primary food items'
      parent_id: 019723aa-3202-70dd-a0c1-3565681dd87a
      image_url: 'https://example.com/main-dishes.jpg'
      is_active: true
      display_order: 77
      sort_order: 1
      attributes:
        color: '#ff0000'
        icon: utensils
    fileParameters: []
    responses:
      -
        status: 201
        content: |-
          {
            "success": true,
            "message": "Category created successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "name": "Main Dishes",
              "slug": "main-dishes",
              "description": "Primary food items",
              "image_url": "https://example.com/main-dishes.jpg",
              "is_active": true,
              "sort_order": 1,
              "is_root": true,
              "has_children": false,
              "has_products": false,
              "products_count": 0,
              "children_count": 0,
              "depth": 0,
              "full_path": "Main Dishes",
              "parent": null,
              "created_at": "2024-01-15T10:30:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/business/categories/{id}'
    metadata:
      groupName: 'Business Categories'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display the specified category.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The category ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Category retrieved successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "name": "Main Dishes",
              "slug": "main-dishes",
              "description": "Primary food items",
              "image_url": "https://example.com/main-dishes.jpg",
              "is_active": true,
              "sort_order": 1,
              "is_root": true,
              "has_children": true,
              "has_products": true,
              "products_count": 15,
              "children_count": 3,
              "depth": 0,
              "full_path": "Main Dishes",
              "breadcrumb": [{"id": "...", "name": "Main Dishes", "slug": "main-dishes"}],
              "parent": null,
              "children": [],
              "attributes": {"color": "#ff0000", "icon": "utensils"},
              "created_at": "2024-01-15T10:30:00Z",
              "updated_at": "2024-01-15T10:30:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
      - PATCH
    uri: 'api/v1/business/categories/{id}'
    metadata:
      groupName: 'Business Categories'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update the specified category.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The category ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 'Category name.'
        required: false
        example: 'Updated Main Dishes'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      description:
        name: description
        description: 'Category description.'
        required: false
        example: 'Updated description'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      parent_id:
        name: parent_id
        description: 'Parent category ID.'
        required: false
        example: 019723aa-3202-70dd-a0c1-3565681dd87b
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      image_url:
        name: image_url
        description: 'Category image URL.'
        required: false
        example: 'https://example.com/updated.jpg'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      is_active:
        name: is_active
        description: 'Category active status.'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      display_order:
        name: display_order
        description: 'Must be at least 0.'
        required: false
        example: 77
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      sort_order:
        name: sort_order
        description: 'Sort order.'
        required: false
        example: 5
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      attributes:
        name: attributes
        description: 'Category attributes.'
        required: false
        example:
          color: '#00ff00'
        type: object
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      name: 'Updated Main Dishes'
      description: 'Updated description'
      parent_id: 019723aa-3202-70dd-a0c1-3565681dd87b
      image_url: 'https://example.com/updated.jpg'
      is_active: false
      display_order: 77
      sort_order: 5
      attributes:
        color: '#00ff00'
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Category updated successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "name": "Updated Main Dishes",
              "updated_at": "2024-01-15T11:00:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/v1/business/categories/{id}'
    metadata:
      groupName: 'Business Categories'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Remove the specified category.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The category ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Category deleted successfully",
            "data": null
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
