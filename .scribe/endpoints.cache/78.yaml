## Autogenerated by Scribe. DO NOT MODIFY.

name: 'Business Analytics'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1/business/analytics/revenue
    metadata:
      groupName: 'Business Analytics'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get revenue analytics with trends and comparisons.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      period:
        name: period
        description: 'Period for analytics (week, month, quarter, year).'
        required: false
        example: month
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      compare_period:
        name: compare_period
        description: 'Period to compare against (previous_period, previous_year).'
        required: false
        example: previous_period
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      timezone:
        name: timezone
        description: 'Timezone for date calculations.'
        required: false
        example: Africa/Lagos
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      period: month
      compare_period: previous_period
      timezone: Africa/Lagos
    bodyParameters:
      period:
        name: period
        description: ''
        required: false
        example: week
        type: string
        enumValues:
          - week
          - month
          - quarter
          - year
        exampleWasSpecified: false
        nullable: false
        custom: []
      compare_period:
        name: compare_period
        description: ''
        required: false
        example: previous_year
        type: string
        enumValues:
          - previous_period
          - previous_year
        exampleWasSpecified: false
        nullable: false
        custom: []
      timezone:
        name: timezone
        description: 'Must not be greater than 50 characters.'
        required: false
        example: Asia/Yekaterinburg
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      period: week
      compare_period: previous_year
      timezone: Asia/Yekaterinburg
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Revenue analytics retrieved successfully",
            "data": {
              "current_period": {
                "period": "month",
                "start_date": "2024-01-01",
                "end_date": "2024-01-31",
                "total_revenue": 750000,
                "orders_count": 150,
                "average_order_value": 5000,
                "paid_orders": 145,
                "pending_orders": 5
              },
              "comparison_period": {
                "period": "previous_month",
                "total_revenue": 650000,
                "orders_count": 130,
                "growth_percentage": 15.38,
                "orders_growth": 15.38
              },
              "daily_breakdown": [
                {
                  "date": "2024-01-01",
                  "revenue": 25000,
                  "orders": 5,
                  "average_order_value": 5000
                }
              ],
              "trends": {
                "revenue_trend": "increasing",
                "orders_trend": "increasing",
                "best_day": "2024-01-15",
                "worst_day": "2024-01-03"
              }
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/business/analytics/products
    metadata:
      groupName: 'Business Analytics'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get product performance analytics.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      period:
        name: period
        description: 'Period for analytics (week, month, quarter, year).'
        required: false
        example: month
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      limit:
        name: limit
        description: 'Number of top products to return.'
        required: false
        example: 10
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      sort_by:
        name: sort_by
        description: 'Sort products by (revenue, orders, quantity).'
        required: false
        example: revenue
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      period: month
      limit: 10
      sort_by: revenue
    bodyParameters:
      period:
        name: period
        description: ''
        required: false
        example: quarter
        type: string
        enumValues:
          - week
          - month
          - quarter
          - year
        exampleWasSpecified: false
        nullable: false
        custom: []
      limit:
        name: limit
        description: 'Must be at least 1. Must not be greater than 50.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      sort_by:
        name: sort_by
        description: ''
        required: false
        example: orders
        type: string
        enumValues:
          - revenue
          - orders
          - quantity
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      period: quarter
      limit: 1
      sort_by: orders
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Product analytics retrieved successfully",
            "data": {
              "top_products": [
                {
                  "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                  "name": "Jollof Rice",
                  "category": "Main Dishes",
                  "total_revenue": 125000,
                  "orders_count": 25,
                  "quantity_sold": 30,
                  "average_price": 4166.67,
                  "growth_percentage": 15.5
                }
              ],
              "category_performance": [
                {
                  "category": "Main Dishes",
                  "revenue": 300000,
                  "orders": 60,
                  "percentage_of_total": 40.0
                }
              ],
              "product_trends": {
                "trending_up": ["Jollof Rice", "Fried Rice"],
                "trending_down": ["Salad"],
                "new_products": ["Pasta"]
              }
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/business/analytics/customers
    metadata:
      groupName: 'Business Analytics'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get customer analytics and insights.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      period:
        name: period
        description: 'Period for analytics (week, month, quarter, year).'
        required: false
        example: month
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      period: month
    bodyParameters:
      period:
        name: period
        description: ''
        required: false
        example: week
        type: string
        enumValues:
          - week
          - month
          - quarter
          - year
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      period: week
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Customer analytics retrieved successfully",
            "data": {
              "customer_metrics": {
                "total_customers": 150,
                "new_customers": 25,
                "returning_customers": 125,
                "retention_rate": 83.33,
                "average_order_frequency": 2.5
              },
              "top_customers": [
                {
                  "customer_name": "John Doe",
                  "total_orders": 15,
                  "total_spent": 75000,
                  "average_order_value": 5000,
                  "last_order_date": "2024-01-25"
                }
              ],
              "customer_segments": {
                "high_value": 15,
                "medium_value": 45,
                "low_value": 90
              }
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
