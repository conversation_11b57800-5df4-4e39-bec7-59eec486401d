## Autogenerated by <PERSON><PERSON><PERSON>. DO NOT MODIFY.

name: 'Provider Settings'
description: ''
endpoints:
  -
    httpMethods:
      - PUT
    uri: api/v1/provider/settings
    metadata:
      groupName: 'Provider Settings'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update provider settings.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      max_orders_per_batch:
        name: max_orders_per_batch
        description: 'Maximum number of orders to handle in one batch. Must be at least 1. Must not be greater than 20.'
        required: false
        example: 5
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      preferred_vehicle_type:
        name: preferred_vehicle_type
        description: 'Preferred vehicle type for deliveries.'
        required: false
        example: motorcycle
        type: string
        enumValues:
          - bicycle
          - motorcycle
          - car
          - van
          - truck
        exampleWasSpecified: false
        nullable: false
        custom: []
      max_delivery_distance_km:
        name: max_delivery_distance_km
        description: 'Maximum delivery distance in kilometers. Must be at least 1. Must not be greater than 50.'
        required: false
        example: 25.0
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      auto_accept_deliveries:
        name: auto_accept_deliveries
        description: 'Automatically accept deliveries that meet criteria.'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      delivery_fee_per_km:
        name: delivery_fee_per_km
        description: 'Fee charged per kilometer. Must be at least 0.'
        required: false
        example: 50.0
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      base_delivery_fee:
        name: base_delivery_fee
        description: 'Base delivery fee. Must be at least 0.'
        required: false
        example: 200.0
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      working_hours:
        name: working_hours
        description: 'Working hours for each day.'
        required: false
        example:
          -
            day: monday
            start_time: '08:00'
            end_time: '18:00'
            is_available: true
        type: 'object[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      break_time_start:
        name: break_time_start
        description: 'Break time start. Must be a valid date in the format <code>H:i</code>.'
        required: false
        example: '12:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      break_time_end:
        name: break_time_end
        description: 'Break time end. Must be a valid date in the format <code>H:i</code>.'
        required: false
        example: '13:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      break_duration_minutes:
        name: break_duration_minutes
        description: 'Must be at least 15. Must not be greater than 120.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      service_areas:
        name: service_areas
        description: 'Service areas and their delivery fees.'
        required: false
        example:
          -
            area_name: 'Victoria Island'
            delivery_fee: 500.0
            estimated_time_minutes: 30
        type: 'object[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      auto_acceptance_criteria:
        name: auto_acceptance_criteria
        description: 'Criteria for automatically accepting deliveries.'
        required: false
        example:
          min_delivery_fee: 300
          max_distance_km: 15
          working_hours_only: true
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      auto_acceptance_criteria.min_delivery_fee:
        name: auto_acceptance_criteria.min_delivery_fee
        description: 'Must be at least 0.'
        required: false
        example: 300.0
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      auto_acceptance_criteria.max_distance_km:
        name: auto_acceptance_criteria.max_distance_km
        description: 'Must be at least 1. Must not be greater than 50.'
        required: false
        example: 15.0
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      auto_acceptance_criteria.working_hours_only:
        name: auto_acceptance_criteria.working_hours_only
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      auto_acceptance_criteria.exclude_rush_hours:
        name: auto_acceptance_criteria.exclude_rush_hours
        description: ''
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notification_settings:
        name: notification_settings
        description: ''
        required: false
        example: null
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notification_settings.new_delivery_sound:
        name: notification_settings.new_delivery_sound
        description: ''
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notification_settings.route_optimization_alerts:
        name: notification_settings.route_optimization_alerts
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notification_settings.earnings_summary:
        name: notification_settings.earnings_summary
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notification_settings.maintenance_reminders:
        name: notification_settings.maintenance_reminders
        description: ''
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      vehicle_settings:
        name: vehicle_settings
        description: 'Vehicle and equipment information.'
        required: false
        example:
          license_plate: ABC-123-XY
          vehicle_model: 'Honda CB150R'
          vehicle_year: 2022
          has_insulated_bag: true
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      vehicle_settings.license_plate:
        name: vehicle_settings.license_plate
        description: 'Must not be greater than 20 characters.'
        required: false
        example: ABC-123-XY
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      vehicle_settings.vehicle_model:
        name: vehicle_settings.vehicle_model
        description: 'Must not be greater than 100 characters.'
        required: false
        example: 'Honda CB150R'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      vehicle_settings.vehicle_year:
        name: vehicle_settings.vehicle_year
        description: 'Must be at least 1990. Must not be greater than 2030.'
        required: false
        example: 2022
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      vehicle_settings.insurance_expiry:
        name: vehicle_settings.insurance_expiry
        description: 'Must be a valid date. Must be a date after <code>today</code>.'
        required: false
        example: '2051-07-02'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      vehicle_settings.has_insulated_bag:
        name: vehicle_settings.has_insulated_bag
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      vehicle_settings.has_gps_tracker:
        name: vehicle_settings.has_gps_tracker
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'working_hours[].day':
        name: 'working_hours[].day'
        description: 'This field is required when <code>working_hours</code> is present.'
        required: false
        example: tuesday
        type: string
        enumValues:
          - monday
          - tuesday
          - wednesday
          - thursday
          - friday
          - saturday
          - sunday
        exampleWasSpecified: false
        nullable: false
        custom: []
      'working_hours[].start_time':
        name: 'working_hours[].start_time'
        description: 'This field is required when <code>working_hours</code> is present. Must be a valid date in the format <code>H:i</code>.'
        required: false
        example: '09:33'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'working_hours[].end_time':
        name: 'working_hours[].end_time'
        description: 'This field is required when <code>working_hours</code> is present. Must be a valid date in the format <code>H:i</code>.'
        required: false
        example: '09:33'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'working_hours[].is_available':
        name: 'working_hours[].is_available'
        description: ''
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'service_areas[].area_name':
        name: 'service_areas[].area_name'
        description: 'This field is required when <code>service_areas</code> is present. Must not be greater than 255 characters.'
        required: false
        example: 'n'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'service_areas[].delivery_fee':
        name: 'service_areas[].delivery_fee'
        description: 'This field is required when <code>service_areas</code> is present. Must be at least 0.'
        required: false
        example: 84
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'service_areas[].estimated_time_minutes':
        name: 'service_areas[].estimated_time_minutes'
        description: 'This field is required when <code>service_areas</code> is present. Must be at least 5. Must not be greater than 180.'
        required: false
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notifications.email_notifications:
        name: notifications.email_notifications
        description: 'Enable email notifications.'
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      notifications.sms_notifications:
        name: notifications.sms_notifications
        description: 'Enable SMS notifications.'
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      notifications.push_notifications:
        name: notifications.push_notifications
        description: 'Enable push notifications.'
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      operating_hours.monday.open:
        name: operating_hours.monday.open
        description: 'Monday opening time.'
        required: false
        example: '08:00'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      operating_hours.monday.close:
        name: operating_hours.monday.close
        description: 'Monday closing time.'
        required: false
        example: '20:00'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      operating_hours.monday.is_active:
        name: operating_hours.monday.is_active
        description: 'Monday active status.'
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      auto_accept.enabled:
        name: auto_accept.enabled
        description: 'Enable auto-accept.'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      auto_accept.minimum_fee:
        name: auto_accept.minimum_fee
        description: 'Minimum fee for auto-accept.'
        required: false
        example: 500
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      auto_accept.maximum_distance:
        name: auto_accept.maximum_distance
        description: 'Maximum distance for auto-accept.'
        required: false
        example: 10
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      pricing.base_fee:
        name: pricing.base_fee
        description: 'Base delivery fee.'
        required: false
        example: 500
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      pricing.per_km_rate:
        name: pricing.per_km_rate
        description: 'Rate per kilometer.'
        required: false
        example: 100
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      max_orders_per_batch: 5
      preferred_vehicle_type: motorcycle
      max_delivery_distance_km: 25.0
      auto_accept_deliveries: false
      delivery_fee_per_km: 50.0
      base_delivery_fee: 200.0
      working_hours:
        -
          day: monday
          start_time: '08:00'
          end_time: '18:00'
          is_available: true
      break_time_start: '12:00'
      break_time_end: '13:00'
      break_duration_minutes: 1
      service_areas:
        -
          area_name: 'Victoria Island'
          delivery_fee: 500.0
          estimated_time_minutes: 30
      auto_acceptance_criteria:
        min_delivery_fee: 300
        max_distance_km: 15
        working_hours_only: true
        exclude_rush_hours: true
      notification_settings:
        new_delivery_sound: true
        route_optimization_alerts: false
        earnings_summary: false
        maintenance_reminders: true
      vehicle_settings:
        license_plate: ABC-123-XY
        vehicle_model: 'Honda CB150R'
        vehicle_year: 2022
        has_insulated_bag: true
        insurance_expiry: '2051-07-02'
        has_gps_tracker: false
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Provider settings updated successfully",
            "data": {
              "notifications": {
                "email_notifications": true,
                "sms_notifications": false
              },
              "auto_accept": {
                "enabled": true,
                "minimum_fee": 600
              }
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
