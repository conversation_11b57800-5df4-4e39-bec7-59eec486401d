## Autogenerated by <PERSON>ri<PERSON>. DO NOT MODIFY.

name: 'Provider Driver Performance'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: 'api/v1/provider/drivers/{driver}/performance'
    metadata:
      groupName: 'Provider Driver Performance'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get driver performance metrics.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      driver:
        name: driver
        description: 'Driver ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      driver: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters:
      start_date:
        name: start_date
        description: 'Start date for metrics (Y-m-d).'
        required: false
        example: '2024-01-01'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      end_date:
        name: end_date
        description: 'End date for metrics (Y-m-d).'
        required: false
        example: '2024-01-31'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      start_date: '2024-01-01'
      end_date: '2024-01-31'
    bodyParameters:
      start_date:
        name: start_date
        description: 'Must be a valid date. Must be a valid date in the format <code>Y-m-d</code>.'
        required: false
        example: '2025-06-08'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      end_date:
        name: end_date
        description: 'Must be a valid date. Must be a valid date in the format <code>Y-m-d</code>. Must be a date after or equal to <code>start_date</code>.'
        required: false
        example: '2051-07-02'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      start_date: '2025-06-08'
      end_date: '2051-07-02'
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Driver performance retrieved successfully",
            "data": {
              "driver_id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "driver_name": "John Doe",
              "period": {
                "start_date": "2024-01-01",
                "end_date": "2024-01-31"
              },
              "delivery_stats": {
                "total_deliveries": 45,
                "completed_deliveries": 42,
                "cancelled_deliveries": 3,
                "completion_rate": 93.33,
                "cancellation_rate": 6.67
              },
              "performance_metrics": {
                "average_delivery_time_minutes": 28.5,
                "on_time_delivery_rate": 89.5,
                "total_distance_km": 245.8
              },
              "ratings": {
                "average_rating": 4.7,
                "total_ratings": 42
              },
              "earnings": {
                "total_earnings": 15750.00,
                "average_per_delivery": 375.00
              },
              "performance_trend": {
                "completion_rate_change": 2.5,
                "on_time_rate_change": -1.2,
                "trend_direction": "improving"
              }
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/provider/drivers/{driver}/performance/rate'
    metadata:
      groupName: 'Provider Driver Performance'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Rate a driver.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      driver:
        name: driver
        description: 'Driver ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      driver: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      rating:
        name: rating
        description: 'Rating from 1 to 5.'
        required: true
        example: 5
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      comment:
        name: comment
        description: 'Optional comment about the driver.'
        required: false
        example: 'Excellent service'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      rating: 5
      comment: 'Excellent service'
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Driver rated successfully",
            "data": {
              "driver_id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "rating": 5,
              "comment": "Excellent service",
              "rated_at": "2024-01-22T15:30:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/provider/drivers/leaderboard
    metadata:
      groupName: 'Provider Driver Performance'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get driver leaderboard.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      metric:
        name: metric
        description: 'Metric to rank by (completion_rate, on_time_rate, average_rating, total_deliveries).'
        required: false
        example: completion_rate
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      limit:
        name: limit
        description: 'Number of drivers to return (max 50).'
        required: false
        example: 10
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      metric: completion_rate
      limit: 10
    bodyParameters:
      metric:
        name: metric
        description: ''
        required: false
        example: completion_rate
        type: string
        enumValues:
          - completion_rate
          - on_time_rate
          - average_rating
          - total_deliveries
        exampleWasSpecified: false
        nullable: false
        custom: []
      limit:
        name: limit
        description: 'Must be at least 1. Must not be greater than 50.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      metric: completion_rate
      limit: 1
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Driver leaderboard retrieved successfully",
            "data": [
              {
                "driver_id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                "driver_name": "John Doe",
                "score": 95.5,
                "total_deliveries": 45,
                "completion_rate": 95.5,
                "average_rating": 4.8
              },
              {
                "driver_id": "019723aa-3202-70dd-a0c1-3565681dd87b",
                "driver_name": "Jane Smith",
                "score": 92.3,
                "total_deliveries": 38,
                "completion_rate": 92.3,
                "average_rating": 4.6
              }
            ]
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
