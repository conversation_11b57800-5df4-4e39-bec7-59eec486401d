## Autogenerated by <PERSON>ri<PERSON>. DO NOT MODIFY.

name: 'Provider Team'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1/provider/team-members
    metadata:
      groupName: 'Provider Team'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get provider team members.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      page:
        name: page
        description: 'Page number for pagination.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'Items per page (max 50).'
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      search:
        name: search
        description: 'Search team members by name or email.'
        required: false
        example: john
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      role:
        name: role
        description: 'Filter by role (driver, dispatcher, manager).'
        required: false
        example: driver
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      is_active:
        name: is_active
        description: 'Filter by active status.'
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      page: 1
      per_page: 15
      search: john
      role: driver
      is_active: true
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Team members retrieved successfully",
            "data": {
              "data": [
                {
                  "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                  "name": "John Driver",
                  "email": "<EMAIL>",
                  "phone": "08012345678",
                  "role": "driver",
                  "is_active": true,
                  "is_available": true,
                  "total_deliveries": 150,
                  "rating": 4.7,
                  "joined_at": "2024-01-15T10:30:00Z"
                }
              ],
              "current_page": 1,
              "per_page": 15,
              "total": 10
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/provider/team-members
    metadata:
      groupName: 'Provider Team'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Add team member.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      first_name:
        name: first_name
        description: "Team member's first name."
        required: true
        example: John
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      last_name:
        name: last_name
        description: "Team member's last name."
        required: true
        example: Driver
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      email:
        name: email
        description: 'Team member email.'
        required: true
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      phone_number:
        name: phone_number
        description: 'Team member phone.'
        required: true
        example: '08012345678'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      role:
        name: role
        description: 'Team member role.'
        required: true
        example: driver
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      password:
        name: password
        description: "Team member's password."
        required: true
        example: password123
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      first_name: John
      last_name: Driver
      email: <EMAIL>
      phone_number: '08012345678'
      role: driver
      password: password123
    fileParameters: []
    responses:
      -
        status: 201
        content: |-
          {
            "success": true,
            "message": "Team member invited successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "name": "John Driver",
              "email": "<EMAIL>",
              "phone": "08012345678",
              "role": "driver",
              "status": "active",
              "availability_status": "offline",
              "created_at": "2024-01-15T10:30:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/provider/team-members/{id}'
    metadata:
      groupName: 'Provider Team'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get specific team member details.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the team member.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      member:
        name: member
        description: 'Team member ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
      member: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Team member retrieved successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "name": "John Driver",
              "email": "<EMAIL>",
              "phone": "08012345678",
              "role": "driver",
              "is_active": true,
              "is_available": true,
              "total_deliveries": 150,
              "completed_deliveries": 145,
              "rating": 4.7,
              "vehicle_info": {
                "type": "motorcycle",
                "plate_number": "ABC123KD",
                "model": "Honda CB150"
              },
              "joined_at": "2024-01-15T10:30:00Z",
              "last_active": "2024-01-20T14:30:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
      - PATCH
    uri: 'api/v1/provider/team-members/{id}'
    metadata:
      groupName: 'Provider Team'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update team member.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the team member.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      member:
        name: member
        description: 'Team member ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
      member: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      first_name:
        name: first_name
        description: "Team member's first name."
        required: false
        example: John
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      last_name:
        name: last_name
        description: "Team member's last name."
        required: false
        example: Driver
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      phone_number:
        name: phone_number
        description: 'Team member phone.'
        required: false
        example: '08012345678'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      role:
        name: role
        description: 'Team member role.'
        required: false
        example: driver
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      availability_status:
        name: availability_status
        description: 'Availability status.'
        required: false
        example: available
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      first_name: John
      last_name: Driver
      phone_number: '08012345678'
      role: driver
      availability_status: available
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Team member updated successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "name": "John Driver",
              "email": "<EMAIL>",
              "phone": "08012345678",
              "role": "driver",
              "status": "active",
              "availability_status": "available",
              "updated_at": "2024-01-15T10:30:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/v1/provider/team-members/{id}'
    metadata:
      groupName: 'Provider Team'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Remove team member.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the team member.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      member:
        name: member
        description: 'Team member ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
      member: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Team member removed successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "name": "John Driver",
              "email": "<EMAIL>",
              "removed_at": "2024-01-15T10:30:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
