name: 'Customer Orders'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1/customer/orders
    metadata:
      groupName: 'Customer Orders'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display a listing of customer orders.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      page:
        name: page
        description: 'Page number for pagination.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'Number of items per page (max 100).'
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      search:
        name: search
        description: 'Search in order reference, business name.'
        required: false
        example: ORD-2024
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      status:
        name: status
        description: 'Filter by order status.'
        required: false
        example: pending
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      payment_status:
        name: payment_status
        description: 'Filter by payment status.'
        required: false
        example: paid
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      date_from:
        name: date_from
        description: 'Filter orders from date (Y-m-d).'
        required: false
        example: '2024-01-01'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      date_to:
        name: date_to
        description: 'Filter orders to date (Y-m-d).'
        required: false
        example: '2024-01-31'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      sort_by:
        name: sort_by
        description: 'Sort by field (created_at, total_amount, status).'
        required: false
        example: created_at
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      sort_direction:
        name: sort_direction
        description: 'Sort direction (asc, desc).'
        required: false
        example: desc
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      page: 1
      per_page: 15
      search: ORD-2024
      status: pending
      payment_status: paid
      date_from: '2024-01-01'
      date_to: '2024-01-31'
      sort_by: created_at
      sort_direction: desc
    bodyParameters:
      page:
        name: page
        description: 'Must be at least 1.'
        required: false
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'Must be at least 1. Must not be greater than 100.'
        required: false
        example: 22
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      search:
        name: search
        description: 'Must not be greater than 255 characters.'
        required: false
        example: g
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      status:
        name: status
        description: ''
        required: false
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      payment_status:
        name: payment_status
        description: ''
        required: false
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      date_from:
        name: date_from
        description: 'Must be a valid date. Must be a valid date in the format <code>Y-m-d</code>.'
        required: false
        example: '2025-06-08'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      date_to:
        name: date_to
        description: 'Must be a valid date. Must be a valid date in the format <code>Y-m-d</code>. Must be a date after or equal to <code>date_from</code>.'
        required: false
        example: '2051-07-02'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      sort_by:
        name: sort_by
        description: ''
        required: false
        example: payment_status
        type: string
        enumValues:
          - created_at
          - total_amount
          - status
          - payment_status
        exampleWasSpecified: false
        nullable: false
        custom: []
      sort_direction:
        name: sort_direction
        description: ''
        required: false
        example: asc
        type: string
        enumValues:
          - asc
          - desc
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      page: 16
      per_page: 22
      search: g
      status: architecto
      payment_status: architecto
      date_from: '2025-06-08'
      date_to: '2051-07-02'
      sort_by: payment_status
      sort_direction: asc
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Orders retrieved successfully",
            "data": {
              "orders": [
                {
                  "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                  "order_reference": "ORD-2024-001",
                  "business": {
                    "id": "019723aa-3202-70dd-a0c1-3565681dd87b",
                    "name": "Pizza Palace",
                    "business_type": "restaurant"
                  },
                  "status": "pending",
                  "payment_status": "pending",
                  "total_amount": 5500,
                  "items_count": 3,
                  "created_at": "2024-01-15T10:30:00Z",
                  "estimated_delivery_time": 45
                }
              ],
              "pagination": {
                "current_page": 1,
                "per_page": 15,
                "total": 150,
                "last_page": 10
              }
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/customer/orders
    metadata:
      groupName: 'Customer Orders'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Create a new order.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      business_id:
        name: business_id
        description: 'The ID of the business to order from.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87c
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      business_branch_id:
        name: business_branch_id
        description: 'The ID of the specific business branch (optional). Must be a valid UUID. The <code>id</code> of an existing record in the business_branches table.'
        required: false
        example: b2c3d4e5-f6g7-8901-bcde-f23456789012
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      order_type:
        name: order_type
        description: 'Type of order (delivery or pickup).'
        required: true
        example: delivery
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      source:
        name: source
        description: 'Source of the order (optional) - "web", "mobile", "api".'
        required: false
        example: mobile
        type: string
        enumValues:
          - web
          - mobile
          - whatsapp
          - api
        exampleWasSpecified: false
        nullable: false
        custom: []
      payment_method:
        name: payment_method
        description: 'Payment method (card, cash, bank_transfer).'
        required: true
        example: card
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      delivery_address_id:
        name: delivery_address_id
        description: 'for delivery orders The delivery address ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87c
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      items:
        name: items
        description: 'Array of order items.'
        required: true
        example:
          - architecto
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      customer_notes:
        name: customer_notes
        description: 'optional Special instructions for the entire order.'
        required: false
        example: 'Please call when ready'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      scheduled_pickup_time:
        name: scheduled_pickup_time
        description: 'optional Scheduled pickup time for pickup orders.'
        required: false
        example: '2024-01-15T14:30:00Z'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      promo_code:
        name: promo_code
        description: 'Promotional code to apply to the order (optional, max 50 characters). Must not be greater than 50 characters.'
        required: false
        example: SAVE10
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      'items[].product_id':
        name: 'items[].product_id'
        description: 'ID of the product to order. Must be a valid UUID. The <code>id</code> of an existing record in the products table.'
        required: true
        example: d4e5f6g7-h8i9-0123-defg-h45678901234
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'items[].quantity':
        name: 'items[].quantity'
        description: 'Quantity of the product (minimum 1, maximum 100). Must be at least 1. Must not be greater than 100.'
        required: true
        example: 2
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'items[].notes':
        name: 'items[].notes'
        description: 'Special notes or instructions for this item (optional, max 500 characters). Must not be greater than 500 characters.'
        required: false
        example: 'Extra spicy please'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      'items[].options':
        name: 'items[].options'
        description: 'Array of product options/customizations (optional).'
        required: false
        example:
          -
            option_id: e5f6g7h8-i9j0-1234-efgh-i56789012345
            value: Large
        type: 'object[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      'items[].options[].option_id':
        name: 'items[].options[].option_id'
        description: 'ID of the product option. Must be a valid UUID. The <code>id</code> of an existing record in the product_options table.'
        required: false
        example: e5f6g7h8-i9j0-1234-efgh-i56789012345
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      'items[].options[].value':
        name: 'items[].options[].value'
        description: 'Selected value for the product option. Must not be greater than 255 characters.'
        required: false
        example: Large
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      'items.*.product_id':
        name: 'items.*.product_id'
        description: 'Product ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87c
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      'items.*.quantity':
        name: 'items.*.quantity'
        description: 'Quantity of the product.'
        required: true
        example: 2
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      'items.*.notes':
        name: 'items.*.notes'
        description: 'optional Special instructions for this item.'
        required: false
        example: 'Extra spicy'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      business_id: 019723aa-3202-70dd-a0c1-3565681dd87c
      business_branch_id: b2c3d4e5-f6g7-8901-bcde-f23456789012
      order_type: delivery
      source: mobile
      payment_method: card
      delivery_address_id: 019723aa-3202-70dd-a0c1-3565681dd87c
      items:
        - architecto
      customer_notes: 'Please call when ready'
      scheduled_pickup_time: '2024-01-15T14:30:00Z'
      promo_code: SAVE10
    fileParameters: []
    responses:
      -
        status: 201
        content: |-
          {
            "success": true,
            "message": "Order created successfully",
            "data": {
              "order": {
                "id": "019723aa-3202-70dd-a0c1-3565681dd87c",
                "order_reference": "ORD-2024-ABC123",
                "business": {
                  "id": "019723aa-3202-70dd-a0c1-3565681dd87c",
                  "business_name": "Pizza Palace",
                  "business_type": "restaurant"
                },
                "status": "pending",
                "payment_status": "pending",
                "order_type": "delivery",
                "sub_total": 5000,
                "delivery_fee": 500,
                "tax_amount": 0,
                "total_amount": 5500,
                "currency": "NGN",
                "payment_method": "card",
                "items": [
                  {
                    "id": "019723aa-3202-70dd-a0c1-3565681dd87c",
                    "product_name": "Margherita Pizza",
                    "quantity": 2,
                    "unit_price": 2500,
                    "total_price": 5000,
                    "notes": "Extra cheese"
                  }
                ],
                "delivery_address": {
                  "address_line_1": "123 Main Street",
                  "city": "Lagos",
                  "state": "Lagos",
                  "country": "Nigeria"
                },
                "customer_notes": "Please call when ready",
                "created_at": "2024-01-15T10:30:00Z"
              }
            }
          }
        headers: []
        description: ''
        custom: []
      -
        status: 422
        content: |-
          {
            "success": false,
            "message": "Validation failed",
            "errors": {
              "business_id": ["Please select a business to order from."],
              "items": ["Please add at least one item to your order."]
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/customer/orders/{id}'
    metadata:
      groupName: 'Customer Orders'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display the specified customer order.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The order ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Order retrieved successfully",
            "data": {
              "order": {
                "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                "order_reference": "ORD-2024-001",
                "business": {
                  "id": "019723aa-3202-70dd-a0c1-3565681dd87b",
                  "name": "Pizza Palace",
                  "business_type": "restaurant",
                  "phone": "+***********-4567"
                },
                "status": "pending",
                "payment_status": "pending",
                "order_type": "delivery",
                "total_amount": 5500,
                "sub_total": 5000,
                "delivery_fee": 500,
                "tax_amount": 0,
                "items": [
                  {
                    "id": "019723aa-3202-70dd-a0c1-3565681dd87c",
                    "product_name": "Margherita Pizza",
                    "quantity": 2,
                    "unit_price": 2500,
                    "total_price": 5000
                  }
                ],
                "delivery_address": {
                  "address_line_1": "123 Main Street",
                  "city": "Lagos",
                  "state": "Lagos",
                  "country": "Nigeria"
                },
                "created_at": "2024-01-15T10:30:00Z",
                "estimated_delivery_time": 45
              }
            }
          }
        headers: []
        description: ''
        custom: []
      -
        status: 404
        content: |-
          {
            "success": false,
            "message": "Order not found"
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/customer/orders/{order}/cancel'
    metadata:
      groupName: 'Customer Orders'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Cancel an order.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      order:
        name: order
        description: 'The order ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87c
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      order: 019723aa-3202-70dd-a0c1-3565681dd87c
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      cancellation_reason:
        name: cancellation_reason
        description: 'Reason for cancellation.'
        required: true
        example: 'Changed my mind'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      cancellation_reason: 'Changed my mind'
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Order cancelled successfully",
            "data": {
              "order": {
                "id": "019723aa-3202-70dd-a0c1-3565681dd87c",
                "status": "cancelled",
                "cancellation_reason": "Changed my mind",
                "cancelled_at": "2024-01-15T11:00:00Z"
              }
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/customer/orders/{order}/rate'
    metadata:
      groupName: 'Customer Orders'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Rate an order.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      order:
        name: order
        description: 'The order ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87c
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      order: 019723aa-3202-70dd-a0c1-3565681dd87c
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      rating:
        name: rating
        description: 'Rating from 1 to 5.'
        required: true
        example: 5
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      review:
        name: review
        description: 'optional Review text.'
        required: false
        example: 'Great food and fast delivery!'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
    cleanBodyParameters:
      rating: 5
      review: 'Great food and fast delivery!'
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Order rated successfully",
            "data": {
              "rating": {
                "id": "019723aa-3202-70dd-a0c1-3565681dd87c",
                "rating": 5,
                "review": "Great food and fast delivery!",
                "created_at": "2024-01-15T12:00:00Z"
              }
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/customer/orders/{order}/track'
    metadata:
      groupName: 'Customer Orders'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Track an order.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      order:
        name: order
        description: 'The order ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87c
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      order: 019723aa-3202-70dd-a0c1-3565681dd87c
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Order tracking retrieved successfully",
            "data": {
              "tracking": {
                "order_id": "019723aa-3202-70dd-a0c1-3565681dd87c",
                "status": "in_transit",
                "estimated_delivery_time": "2024-01-15T13:30:00Z",
                "delivery_provider": {
                  "name": "FastDelivery",
                  "phone": "+234123456789"
                },
                "driver": {
                  "name": "John Doe",
                  "phone": "+234987654321",
                  "location": {
                    "latitude": 6.5244,
                    "longitude": 3.3792
                  }
                },
                "timeline": [
                  {
                    "status": "pending",
                    "timestamp": "2024-01-15T10:30:00Z",
                    "description": "Order placed"
                  },
                  {
                    "status": "confirmed",
                    "timestamp": "2024-01-15T10:35:00Z",
                    "description": "Order confirmed by restaurant"
                  },
                  {
                    "status": "preparing",
                    "timestamp": "2024-01-15T10:40:00Z",
                    "description": "Order is being prepared"
                  },
                  {
                    "status": "ready",
                    "timestamp": "2024-01-15T11:10:00Z",
                    "description": "Order ready for pickup"
                  },
                  {
                    "status": "in_transit",
                    "timestamp": "2024-01-15T11:15:00Z",
                    "description": "Order picked up by driver"
                  }
                ]
              }
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
