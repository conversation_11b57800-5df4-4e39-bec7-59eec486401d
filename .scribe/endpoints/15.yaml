name: 'Customer Product Reviews'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: 'api/v1/customer/products/{product}/reviews'
    metadata:
      groupName: 'Customer Product Reviews'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get reviews for a product.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      product:
        name: product
        description: 'Product ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      product: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters:
      rating:
        name: rating
        description: 'Filter by specific rating (1-5).'
        required: false
        example: 5
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      min_rating:
        name: min_rating
        description: 'Filter by minimum rating (1-5).'
        required: false
        example: 4
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      verified_only:
        name: verified_only
        description: 'Show only verified purchase reviews.'
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      with_images:
        name: with_images
        description: 'Show only reviews with images.'
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'Items per page (1-100).'
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      rating: 5
      min_rating: 4
      verified_only: true
      with_images: true
      per_page: 15
    bodyParameters:
      rating:
        name: rating
        description: 'Must be at least 1. Must not be greater than 5.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      min_rating:
        name: min_rating
        description: 'Must be at least 1. Must not be greater than 5.'
        required: false
        example: 2
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      verified_only:
        name: verified_only
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      with_images:
        name: with_images
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'Must be at least 1. Must not be greater than 100.'
        required: false
        example: 7
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      rating: 1
      min_rating: 2
      verified_only: false
      with_images: false
      per_page: 7
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Product reviews retrieved successfully",
            "data": [
              {
                "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                "rating": 5,
                "review": "Excellent product! Highly recommended.",
                "images": ["https://example.com/review1.jpg"],
                "is_verified_purchase": true,
                "customer": {
                  "name": "John D.",
                  "verified_buyer": true
                },
                "created_at": "2024-01-22T15:30:00Z"
              }
            ],
            "meta": {
              "current_page": 1,
              "per_page": 15,
              "total": 25,
              "last_page": 2
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/customer/products/{product}/reviews'
    metadata:
      groupName: 'Customer Product Reviews'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Create a product review.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      product:
        name: product
        description: 'Product ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      product: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      rating:
        name: rating
        description: 'Rating from 1 to 5.'
        required: true
        example: 5
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      review:
        name: review
        description: 'Optional review text.'
        required: false
        example: '"Excellent product! Highly recommended."'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      images:
        name: images
        description: 'Optional array of image URLs.'
        required: false
        example:
          - 'https://example.com/review1.jpg'
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      order_id:
        name: order_id
        description: 'Optional order ID for verified purchase.'
        required: false
        example: 019723aa-3202-70dd-a0c1-3565681dd87b
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      rating: 5
      review: '"Excellent product! Highly recommended."'
      images:
        - 'https://example.com/review1.jpg'
      order_id: 019723aa-3202-70dd-a0c1-3565681dd87b
    fileParameters: []
    responses:
      -
        status: 201
        content: |-
          {
            "success": true,
            "message": "Product review created successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "rating": 5,
              "review": "Excellent product! Highly recommended.",
              "images": ["https://example.com/review1.jpg"],
              "is_verified_purchase": true,
              "created_at": "2024-01-22T15:30:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
      -
        status: 422
        content: |-
          {
            "success": false,
            "message": "You have already reviewed this product",
            "error_code": "REVIEW_ALREADY_EXISTS"
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/v1/customer/reviews/{review}'
    metadata:
      groupName: 'Customer Product Reviews'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update a product review.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      review:
        name: review
        description: 'Review ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      review: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      rating:
        name: rating
        description: 'Optional rating from 1 to 5.'
        required: false
        example: 4
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      review:
        name: review
        description: 'Optional review text.'
        required: false
        example: '"Updated review text."'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      images:
        name: images
        description: 'Optional array of image URLs.'
        required: false
        example:
          - 'https://example.com/review2.jpg'
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      rating: 4
      review: '"Updated review text."'
      images:
        - 'https://example.com/review2.jpg'
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Product review updated successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "rating": 4,
              "review": "Updated review text.",
              "images": ["https://example.com/review2.jpg"],
              "is_verified_purchase": true,
              "updated_at": "2024-01-22T16:30:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/v1/customer/reviews/{review}'
    metadata:
      groupName: 'Customer Product Reviews'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Delete a product review.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      review:
        name: review
        description: 'Review ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      review: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Product review deleted successfully"
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/customer/my-reviews
    metadata:
      groupName: 'Customer Product Reviews'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Get customer's own reviews."
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      business_id:
        name: business_id
        description: 'Filter by business ID.'
        required: false
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'Items per page (1-100).'
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      business_id: 019723aa-3202-70dd-a0c1-3565681dd87a
      per_page: 15
    bodyParameters:
      business_id:
        name: business_id
        description: 'Must be a valid UUID. The <code>id</code> of an existing record in the businesses table.'
        required: false
        example: 6ff8f7f6-1eb3-3525-be4a-3932c805afed
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'Must be at least 1. Must not be greater than 100.'
        required: false
        example: 7
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      business_id: 6ff8f7f6-1eb3-3525-be4a-3932c805afed
      per_page: 7
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Customer reviews retrieved successfully",
            "data": [
              {
                "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                "rating": 5,
                "review": "Great product!",
                "product": {
                  "id": "019723aa-3202-70dd-a0c1-3565681dd87b",
                  "name": "Premium Coffee",
                  "slug": "premium-coffee"
                },
                "business": {
                  "id": "019723aa-3202-70dd-a0c1-3565681dd87c",
                  "name": "Coffee Shop"
                },
                "is_verified_purchase": true,
                "created_at": "2024-01-22T15:30:00Z"
              }
            ]
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
