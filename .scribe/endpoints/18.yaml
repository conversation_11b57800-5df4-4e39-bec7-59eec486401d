name: 'Order Tracking (Customer)'
description: |-

  APIs for customers to track their orders in real-time
endpoints:
  -
    httpMethods:
      - GET
    uri: 'api/v1/customer/tracking/orders/{order_id}'
    metadata:
      groupName: 'Order Tracking (Customer)'
      groupDescription: |-

        APIs for customers to track their orders in real-time
      subgroup: ''
      subgroupDescription: ''
      title: 'Track Order'
      description: "Get real-time tracking information for a customer's order."
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      order_id:
        name: order_id
        description: 'The order ID to track.'
        required: true
        example: uuid
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      order_id: uuid
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Order tracking information retrieved successfully",
            "data": {
              "order": {
                "id": "uuid",
                "order_reference": "ORD-2024-ABC123",
                "status": "in_transit",
                "status_display": "Out for Delivery",
                "created_at": "2024-01-15T09:00:00Z",
                "estimated_delivery_time": "2024-01-15T11:15:00Z"
              },
              "business": {
                "id": "uuid",
                "name": "Mario's Pizza",
                "phone": "+2348123456789",
                "address": "123 Business St, Lagos"
              },
              "delivery": {
                "id": "uuid",
                "status": "in_transit",
                "driver": {
                  "id": "uuid",
                  "name": "John Driver",
                  "phone": "+2348987654321"
                },
                "current_location": {
                  "latitude": 6.5244,
                  "longitude": 3.3792,
                  "timestamp": "2024-01-15T10:30:00Z"
                },
                "pickup_address": "123 Business St, Lagos",
                "delivery_address": "456 Customer Ave, Lagos"
              },
              "timeline": [
                {
                  "status": "pending",
                  "timestamp": "2024-01-15T09:00:00Z",
                  "description": "Order placed"
                },
                {
                  "status": "confirmed",
                  "timestamp": "2024-01-15T09:05:00Z",
                  "description": "Order confirmed by restaurant"
                }
              ]
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/customer/tracking/orders/{order_id}/timeline'
    metadata:
      groupName: 'Order Tracking (Customer)'
      groupDescription: |-

        APIs for customers to track their orders in real-time
      subgroup: ''
      subgroupDescription: ''
      title: 'Get Order Timeline'
      description: 'Get the timeline of status changes for an order.'
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      order_id:
        name: order_id
        description: 'The order ID.'
        required: true
        example: uuid
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      order_id: uuid
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Order timeline retrieved successfully",
            "data": [
              {
                "status": "pending",
                "timestamp": "2024-01-15T09:00:00Z",
                "description": "Order placed",
                "icon": "clock"
              },
              {
                "status": "confirmed",
                "timestamp": "2024-01-15T09:05:00Z",
                "description": "Order confirmed by restaurant",
                "icon": "check"
              },
              {
                "status": "preparing",
                "timestamp": "2024-01-15T09:10:00Z",
                "description": "Order is being prepared",
                "icon": "cooking"
              }
            ]
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/customer/tracking/active-orders
    metadata:
      groupName: 'Order Tracking (Customer)'
      groupDescription: |-

        APIs for customers to track their orders in real-time
      subgroup: ''
      subgroupDescription: ''
      title: "Get Customer's Active Orders"
      description: 'Get all active orders for the current customer that can be tracked.'
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Active orders retrieved successfully",
            "data": [
              {
                "order_id": "uuid",
                "order_reference": "ORD-2024-ABC123",
                "business_name": "Mario's Pizza",
                "status": "in_transit",
                "status_display": "Out for Delivery",
                "estimated_delivery_time": "2024-01-15T11:15:00Z",
                "has_live_tracking": true,
                "total_amount": "2500.00"
              }
            ]
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
