name: 'Financial Management'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1/admin/financial/platform/summary
    metadata:
      groupName: 'Financial Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get platform financial summary.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      start_date:
        name: start_date
        description: 'Must be a valid date.'
        required: false
        example: '2025-06-08T09:33:09'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      end_date:
        name: end_date
        description: 'Must be a valid date. Must be a date after or equal to <code>start_date</code>.'
        required: false
        example: '2051-07-02'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      start_date: '2025-06-08T09:33:09'
      end_date: '2051-07-02'
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "data": {
              "platform_balance": 150000.00,
              "currency": "NGN",
              "total_commission_revenue": 75000.00,
              "order_commissions": {
                "total_amount": 45000.00,
                "count": 1500,
                "total_order_value": 3000000.00
              },
              "delivery_commissions": {
                "total_amount": 30000.00,
                "count": 1000,
                "total_delivery_value": 1500000.00
              },
              "period": {
                "start_date": "2024-01-01",
                "end_date": "2024-12-31"
              }
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/admin/financial/platform/account
    metadata:
      groupName: 'Financial Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get platform account details.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "data": {
              "id": "account-uuid",
              "accountable_id": "platform",
              "accountable_type": "platform",
              "balance": 150000.00,
              "currency": "NGN",
              "created_at": "2024-01-01T00:00:00.000000Z",
              "updated_at": "2024-12-01T12:00:00.000000Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/admin/financial/businesses/{business}/summary'
    metadata:
      groupName: 'Financial Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get business financial summary.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      business:
        name: business
        description: 'The business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      business_id:
        name: business_id
        description: 'The business ID.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      business: architecto
      business_id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      start_date:
        name: start_date
        description: 'Must be a valid date.'
        required: false
        example: '2025-06-08T09:33:09'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      end_date:
        name: end_date
        description: 'Must be a valid date. Must be a date after or equal to <code>start_date</code>.'
        required: false
        example: '2051-07-02'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      start_date: '2025-06-08T09:33:09'
      end_date: '2051-07-02'
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "data": {
              "business_id": "business-uuid",
              "business_name": "Sample Restaurant",
              "current_balance": 25000.00,
              "currency": "NGN",
              "total_commissions_paid": 15000.00,
              "total_orders_value": 1000000.00,
              "commission_count": 500,
              "average_commission_rate": 1.5,
              "period": {
                "start_date": "2024-01-01",
                "end_date": "2024-12-31"
              }
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/admin/financial/businesses/{business}/account'
    metadata:
      groupName: 'Financial Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get business account details.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      business:
        name: business
        description: 'The business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      business_id:
        name: business_id
        description: 'The business ID.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      business: architecto
      business_id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "data": {
              "id": "account-uuid",
              "accountable_id": "business-uuid",
              "accountable_type": "business",
              "balance": 25000.00,
              "currency": "NGN",
              "created_at": "2024-01-01T00:00:00.000000Z",
              "updated_at": "2024-12-01T12:00:00.000000Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/admin/financial/providers/{provider}/summary'
    metadata:
      groupName: 'Financial Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get provider financial summary.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      provider:
        name: provider
        description: 'The provider.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      provider_id:
        name: provider_id
        description: 'The provider ID.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      provider: architecto
      provider_id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      start_date:
        name: start_date
        description: 'Must be a valid date.'
        required: false
        example: '2025-06-08T09:33:09'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      end_date:
        name: end_date
        description: 'Must be a valid date. Must be a date after or equal to <code>start_date</code>.'
        required: false
        example: '2051-07-02'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      start_date: '2025-06-08T09:33:09'
      end_date: '2051-07-02'
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "data": {
              "provider_id": "provider-uuid",
              "provider_name": "Fast Delivery Co",
              "current_balance": 18000.00,
              "currency": "NGN",
              "total_commissions_paid": 12000.00,
              "total_deliveries_value": 600000.00,
              "commission_count": 300,
              "average_commission_rate": 2.0,
              "period": {
                "start_date": "2024-01-01",
                "end_date": "2024-12-31"
              }
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/admin/financial/providers/{provider}/account'
    metadata:
      groupName: 'Financial Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get provider account details.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      provider:
        name: provider
        description: 'The provider.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      provider_id:
        name: provider_id
        description: 'The provider ID.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      provider: architecto
      provider_id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "data": {
              "id": "account-uuid",
              "accountable_id": "provider-uuid",
              "accountable_type": "delivery_provider",
              "balance": 18000.00,
              "currency": "NGN",
              "created_at": "2024-01-01T00:00:00.000000Z",
              "updated_at": "2024-12-01T12:00:00.000000Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
