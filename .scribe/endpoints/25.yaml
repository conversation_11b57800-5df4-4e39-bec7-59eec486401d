name: 'Admin Campaigns'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1/admin/campaigns
    metadata:
      groupName: 'Admin Campaigns'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get campaign overview.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:09.241698Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: d3f9cefb-2bc7-4e49-9cd4-939aaefb8344
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-mUVF5dPonsZW4IRFUIEhdw==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/admin/campaigns/analytics
    metadata:
      groupName: 'Admin Campaigns'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get campaign analytics.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      status:
        name: status
        description: ''
        required: false
        example: draft
        type: string
        enumValues:
          - draft
          - active
          - paused
          - completed
          - cancelled
        exampleWasSpecified: false
        nullable: true
        custom: []
      type:
        name: type
        description: ''
        required: false
        example: multi_channel
        type: string
        enumValues:
          - email
          - sms
          - push
          - whatsapp
          - multi_channel
        exampleWasSpecified: false
        nullable: true
        custom: []
      date_from:
        name: date_from
        description: 'Must be a valid date.'
        required: false
        example: '2025-06-08T09:33:09'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      date_to:
        name: date_to
        description: 'Must be a valid date. Must be a date after or equal to <code>date_from</code>.'
        required: false
        example: '2051-07-02'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      status: draft
      type: multi_channel
      date_from: '2025-06-08T09:33:09'
      date_to: '2051-07-02'
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:09.246798Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: e8fe2f26-e2cd-45df-be06-5770289fbc83
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-uISGeij+9roMUAmKZFYojw==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/admin/campaigns
    metadata:
      groupName: 'Admin Campaigns'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Create a new campaign.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 'Must not be greater than 255 characters.'
        required: true
        example: b
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      description:
        name: description
        description: 'Must not be greater than 1000 characters.'
        required: false
        example: 'Et animi quos velit et fugiat.'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      type:
        name: type
        description: ''
        required: true
        example: whatsapp
        type: string
        enumValues:
          - email
          - sms
          - push
          - whatsapp
          - multi_channel
        exampleWasSpecified: false
        nullable: false
        custom: []
      channels:
        name: channels
        description: ''
        required: false
        example:
          - sms
        type: 'string[]'
        enumValues:
          - email
          - sms
          - push
          - whatsapp
        exampleWasSpecified: false
        nullable: false
        custom: []
      subject:
        name: subject
        description: 'Must not be greater than 255 characters.'
        required: false
        example: d
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      message:
        name: message
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      template_id:
        name: template_id
        description: 'Must be a valid UUID.'
        required: false
        example: a4855dc5-0acb-33c3-b921-f4291f719ca0
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      variables:
        name: variables
        description: ''
        required: false
        example: null
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      send_immediately:
        name: send_immediately
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      scheduled_at:
        name: scheduled_at
        description: 'Must be a valid date. Must be a date after <code>now</code>.'
        required: false
        example: '2051-07-02'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      timezone:
        name: timezone
        description: ''
        required: false
        example: Asia/Yekaterinburg
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      frequency:
        name: frequency
        description: ''
        required: false
        example: daily
        type: string
        enumValues:
          - once
          - daily
          - weekly
          - monthly
        exampleWasSpecified: false
        nullable: true
        custom: []
      ab_test_enabled:
        name: ab_test_enabled
        description: ''
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      total_budget:
        name: total_budget
        description: 'Must be at least 0.'
        required: false
        example: 39
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      daily_budget:
        name: daily_budget
        description: 'Must be at least 0.'
        required: false
        example: 84
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      currency:
        name: currency
        description: 'Must be 3 characters.'
        required: false
        example: zmi
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      audiences:
        name: audiences
        description: 'Must have at least 1 items.'
        required: true
        example:
          - []
        type: 'object[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'audiences[].name':
        name: 'audiences[].name'
        description: 'Must not be greater than 255 characters.'
        required: true
        example: b
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'audiences[].criteria':
        name: 'audiences[].criteria'
        description: 'Must have at least 1 items.'
        required: true
        example:
          - []
        type: 'object[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'audiences[].criteria[].field':
        name: 'audiences[].criteria[].field'
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'audiences[].criteria[].operator':
        name: 'audiences[].criteria[].operator'
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'audiences[].criteria[].value':
        name: 'audiences[].criteria[].value'
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      name: b
      description: 'Et animi quos velit et fugiat.'
      type: whatsapp
      channels:
        - sms
      subject: d
      message: architecto
      template_id: a4855dc5-0acb-33c3-b921-f4291f719ca0
      send_immediately: false
      scheduled_at: '2051-07-02'
      timezone: Asia/Yekaterinburg
      frequency: daily
      ab_test_enabled: true
      total_budget: 39
      daily_budget: 84
      currency: zmi
      audiences:
        -
          name: b
          criteria:
            -
              field: architecto
              operator: architecto
              value: architecto
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/admin/campaigns/{campaign}'
    metadata:
      groupName: 'Admin Campaigns'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get a specific campaign.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      campaign:
        name: campaign
        description: 'The campaign.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      campaign: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:09.253329Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 31814fb7-b41a-4d23-a44a-57967a544a41
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-4yKJdZQ6ayd/96WgmVqeZA==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/v1/admin/campaigns/{campaign}'
    metadata:
      groupName: 'Admin Campaigns'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update a campaign.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      campaign:
        name: campaign
        description: 'The campaign.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      campaign: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 'Must not be greater than 255 characters.'
        required: false
        example: b
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      description:
        name: description
        description: 'Must not be greater than 1000 characters.'
        required: false
        example: 'Et animi quos velit et fugiat.'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      type:
        name: type
        description: ''
        required: false
        example: multi_channel
        type: string
        enumValues:
          - email
          - sms
          - push
          - whatsapp
          - multi_channel
        exampleWasSpecified: false
        nullable: false
        custom: []
      channels:
        name: channels
        description: ''
        required: false
        example:
          - push
        type: 'string[]'
        enumValues:
          - email
          - sms
          - push
          - whatsapp
        exampleWasSpecified: false
        nullable: false
        custom: []
      status:
        name: status
        description: ''
        required: false
        example: cancelled
        type: string
        enumValues:
          - draft
          - active
          - paused
          - completed
          - cancelled
        exampleWasSpecified: false
        nullable: false
        custom: []
      content:
        name: content
        description: ''
        required: false
        example: null
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      content.subject:
        name: content.subject
        description: 'Must not be greater than 255 characters.'
        required: false
        example: d
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      content.message:
        name: content.message
        description: ''
        required: false
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      content.template_id:
        name: content.template_id
        description: 'Must be a valid UUID.'
        required: false
        example: a4855dc5-0acb-33c3-b921-f4291f719ca0
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      content.variables:
        name: content.variables
        description: ''
        required: false
        example: null
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      settings:
        name: settings
        description: ''
        required: false
        example: null
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.send_immediately:
        name: settings.send_immediately
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      settings.scheduled_at:
        name: settings.scheduled_at
        description: 'Must be a valid date. Must be a date after <code>now</code>.'
        required: false
        example: '2051-07-02'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      settings.timezone:
        name: settings.timezone
        description: ''
        required: false
        example: Asia/Yekaterinburg
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      settings.frequency:
        name: settings.frequency
        description: ''
        required: false
        example: monthly
        type: string
        enumValues:
          - once
          - daily
          - weekly
          - monthly
        exampleWasSpecified: false
        nullable: true
        custom: []
      settings.ab_test_enabled:
        name: settings.ab_test_enabled
        description: ''
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      budget:
        name: budget
        description: ''
        required: false
        example: null
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      budget.total_budget:
        name: budget.total_budget
        description: 'Must be at least 0.'
        required: false
        example: 39
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      budget.daily_budget:
        name: budget.daily_budget
        description: 'Must be at least 0.'
        required: false
        example: 84
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      budget.currency:
        name: budget.currency
        description: 'Must be 3 characters.'
        required: false
        example: zmi
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      name: b
      description: 'Et animi quos velit et fugiat.'
      type: multi_channel
      channels:
        - push
      status: cancelled
      content:
        subject: d
        message: architecto
        template_id: a4855dc5-0acb-33c3-b921-f4291f719ca0
      settings:
        send_immediately: false
        scheduled_at: '2051-07-02'
        timezone: Asia/Yekaterinburg
        frequency: monthly
        ab_test_enabled: true
      budget:
        total_budget: 39
        daily_budget: 84
        currency: zmi
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/admin/campaigns/{campaign}/launch'
    metadata:
      groupName: 'Admin Campaigns'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Launch a campaign.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      campaign:
        name: campaign
        description: 'The campaign.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      campaign: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/admin/campaigns/{campaign}/pause'
    metadata:
      groupName: 'Admin Campaigns'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Pause a campaign.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      campaign:
        name: campaign
        description: 'The campaign.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      campaign: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      reason:
        name: reason
        description: 'Must not be greater than 500 characters.'
        required: false
        example: b
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      reason: b
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/admin/campaigns/{campaign}/recipients'
    metadata:
      groupName: 'Admin Campaigns'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get campaign recipients.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      campaign:
        name: campaign
        description: 'The campaign.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      campaign: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      limit:
        name: limit
        description: 'Must be at least 1. Must not be greater than 1000.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      limit: 1
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:09.262069Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 2ea75314-9c59-4d2b-87e2-464fbac589d9
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-FPDZlgvrTp14xWc1Do7uaQ==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/v1/admin/campaigns/{campaign}/metrics'
    metadata:
      groupName: 'Admin Campaigns'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update campaign metrics.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      campaign:
        name: campaign
        description: 'The campaign.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      campaign: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      total_sent:
        name: total_sent
        description: 'Must be at least 0.'
        required: false
        example: 27
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      total_delivered:
        name: total_delivered
        description: 'Must be at least 0.'
        required: false
        example: 39
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      total_opened:
        name: total_opened
        description: 'Must be at least 0.'
        required: false
        example: 84
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      total_clicked:
        name: total_clicked
        description: 'Must be at least 0.'
        required: false
        example: 12
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      total_conversions:
        name: total_conversions
        description: 'Must be at least 0.'
        required: false
        example: 77
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      total_unsubscribed:
        name: total_unsubscribed
        description: 'Must be at least 0.'
        required: false
        example: 8
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      revenue_generated:
        name: revenue_generated
        description: 'Must be at least 0.'
        required: false
        example: 76
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      cost_spent:
        name: cost_spent
        description: 'Must be at least 0.'
        required: false
        example: 60
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      total_sent: 27
      total_delivered: 39
      total_opened: 84
      total_clicked: 12
      total_conversions: 77
      total_unsubscribed: 8
      revenue_generated: 76
      cost_spent: 60
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/admin/campaigns/{campaign}/ab-test-results'
    metadata:
      groupName: 'Admin Campaigns'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get A/B test results.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      campaign:
        name: campaign
        description: 'The campaign.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      campaign: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:09.268311Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: e93290fd-7223-42d2-ae9f-59809f6ca6ae
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-RZ86HET0EsLApCZTd1CZPw==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/v1/admin/campaigns/{campaign}'
    metadata:
      groupName: 'Admin Campaigns'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Delete a campaign.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      campaign:
        name: campaign
        description: 'The campaign.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      campaign: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
