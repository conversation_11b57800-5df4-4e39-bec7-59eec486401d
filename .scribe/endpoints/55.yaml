name: 'Admin Zones'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1/admin/zones
    metadata:
      groupName: 'Admin Zones'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get all delivery zones.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:09.771675Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 749febbf-6cdb-445b-8958-f67992ea452f
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-/ZUYj/UFkFYHpG/4M+/Ndw==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/admin/zones
    metadata:
      groupName: 'Admin Zones'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Create new delivery zone.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 'Must not be greater than 255 characters.'
        required: true
        example: b
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      zone_type:
        name: zone_type
        description: ''
        required: true
        example: interstate
        type: string
        enumValues:
          - city
          - state
          - interstate
          - polygon
        exampleWasSpecified: false
        nullable: false
        custom: []
      states:
        name: states
        description: 'Must be a valid UUID. The <code>id</code> of an existing record in the states table.'
        required: false
        example:
          - a4855dc5-0acb-33c3-b921-f4291f719ca0
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      cities:
        name: cities
        description: 'Must be a valid UUID. The <code>id</code> of an existing record in the cities table.'
        required: false
        example:
          - c90237e9-ced5-3af6-88ea-84aeaa148878
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      polygon_coordinates:
        name: polygon_coordinates
        description: 'This field is required when <code>zone_type</code> is <code>polygon</code>.'
        required: false
        example: null
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      base_multiplier:
        name: base_multiplier
        description: 'Must be at least 0.1. Must not be greater than 10.'
        required: true
        example: 5
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      description:
        name: description
        description: 'Must not be greater than 1000 characters.'
        required: false
        example: 'Fugiat sunt nihil accusantium harum mollitia.'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      is_active:
        name: is_active
        description: ''
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      name: b
      zone_type: interstate
      states:
        - a4855dc5-0acb-33c3-b921-f4291f719ca0
      cities:
        - c90237e9-ced5-3af6-88ea-84aeaa148878
      base_multiplier: 5
      description: 'Fugiat sunt nihil accusantium harum mollitia.'
      is_active: true
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/admin/zones/analytics
    metadata:
      groupName: 'Admin Zones'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get zone coverage analytics.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      period:
        name: period
        description: ''
        required: false
        example: week
        type: string
        enumValues:
          - today
          - week
          - month
          - quarter
          - year
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      period: week
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:09.777464Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: bd91483f-b46b-41d9-b44f-bd1174e568e6
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-4XBbPdz4sC7JzOKOdEQrqg==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/admin/zones/{zone}'
    metadata:
      groupName: 'Admin Zones'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get zone details.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      zone:
        name: zone
        description: 'The zone.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      zone: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:09.781412Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 812e9542-1bc1-4ccc-84cc-2277692a60d5
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-RxWmEfBdhiROHKuWzP7dlw==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/v1/admin/zones/{zone}'
    metadata:
      groupName: 'Admin Zones'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update delivery zone.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      zone:
        name: zone
        description: 'The zone.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      zone: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 'Must not be greater than 255 characters.'
        required: false
        example: b
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      zone_type:
        name: zone_type
        description: ''
        required: false
        example: interstate
        type: string
        enumValues:
          - city
          - state
          - interstate
          - polygon
        exampleWasSpecified: false
        nullable: false
        custom: []
      states:
        name: states
        description: 'Must be a valid UUID. The <code>id</code> of an existing record in the states table.'
        required: false
        example:
          - a4855dc5-0acb-33c3-b921-f4291f719ca0
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      cities:
        name: cities
        description: 'Must be a valid UUID. The <code>id</code> of an existing record in the cities table.'
        required: false
        example:
          - c90237e9-ced5-3af6-88ea-84aeaa148878
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      polygon_coordinates:
        name: polygon_coordinates
        description: ''
        required: false
        example: null
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      base_multiplier:
        name: base_multiplier
        description: 'Must be at least 0.1. Must not be greater than 10.'
        required: false
        example: 5
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      description:
        name: description
        description: 'Must not be greater than 1000 characters.'
        required: false
        example: 'Fugiat sunt nihil accusantium harum mollitia.'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      is_active:
        name: is_active
        description: ''
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      name: b
      zone_type: interstate
      states:
        - a4855dc5-0acb-33c3-b921-f4291f719ca0
      cities:
        - c90237e9-ced5-3af6-88ea-84aeaa148878
      base_multiplier: 5
      description: 'Fugiat sunt nihil accusantium harum mollitia.'
      is_active: true
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/v1/admin/zones/{zone}'
    metadata:
      groupName: 'Admin Zones'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Delete delivery zone.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      zone:
        name: zone
        description: 'The zone.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      zone: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
