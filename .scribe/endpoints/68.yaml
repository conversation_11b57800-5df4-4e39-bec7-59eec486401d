name: 'Business Team'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1/business/team-members
    metadata:
      groupName: 'Business Team'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get business team members.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      page:
        name: page
        description: 'Page number for pagination.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'Items per page (max 100).'
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      search:
        name: search
        description: 'Search team members by name or email.'
        required: false
        example: john
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      role:
        name: role
        description: 'Filter by role.'
        required: false
        example: admin
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      status:
        name: status
        description: 'Filter by status (active/inactive).'
        required: false
        example: active
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      page: 1
      per_page: 15
      search: john
      role: admin
      status: active
    bodyParameters:
      page:
        name: page
        description: 'Must be at least 1.'
        required: false
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'Must be at least 1. Must not be greater than 100.'
        required: false
        example: 22
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      search:
        name: search
        description: 'Must not be greater than 255 characters.'
        required: false
        example: g
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      role:
        name: role
        description: 'Must not be greater than 50 characters.'
        required: false
        example: z
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      status:
        name: status
        description: ''
        required: false
        example: inactive
        type: string
        enumValues:
          - active
          - inactive
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      page: 16
      per_page: 22
      search: g
      role: z
      status: inactive
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Team members retrieved successfully",
            "data": {
              "data": [
                {
                  "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                  "name": "John Doe",
                  "email": "<EMAIL>",
                  "is_active": true,
                  "created_at": "2024-01-15T10:30:00Z",
                  "roles": ["manager"]
                }
              ],
              "current_page": 1,
              "per_page": 15,
              "total": 5
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/business/team-members
    metadata:
      groupName: 'Business Team'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Invite new team member.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      first_name:
        name: first_name
        description: "Team member's first name."
        required: true
        example: John
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      last_name:
        name: last_name
        description: "Team member's last name."
        required: true
        example: Doe
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      email:
        name: email
        description: "Team member's email address."
        required: true
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      phone_number:
        name: phone_number
        description: "Team member's phone number."
        required: true
        example: '***********'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      role:
        name: role
        description: "Team member's role."
        required: true
        example: business-staff
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      password:
        name: password
        description: "Team member's password."
        required: true
        example: password123
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      first_name: John
      last_name: Doe
      email: <EMAIL>
      phone_number: '***********'
      role: business-staff
      password: password123
    fileParameters: []
    responses:
      -
        status: 201
        content: |-
          {
            "success": true,
            "message": "Team member invited successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "name": "John Doe",
              "email": "<EMAIL>",
              "phone": "***********",
              "role": "business-staff",
              "status": "active",
              "created_at": "2024-01-15T10:30:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/business/team-members/{id}'
    metadata:
      groupName: 'Business Team'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get specific team member details.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the team member.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      member:
        name: member
        description: 'Team member ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
      member: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Team member retrieved successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "name": "John Doe",
              "email": "<EMAIL>",
              "is_active": true,
              "created_at": "2024-01-15T10:30:00Z",
              "roles": ["manager"],
              "permissions": ["manage_orders", "view_analytics"]
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
      - PATCH
    uri: 'api/v1/business/team-members/{id}'
    metadata:
      groupName: 'Business Team'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update team member.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the team member.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      first_name:
        name: first_name
        description: "Team member's first name."
        required: false
        example: John
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      last_name:
        name: last_name
        description: "Team member's last name."
        required: false
        example: Doe
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      phone_number:
        name: phone_number
        description: "Team member's phone number."
        required: false
        example: '***********'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      is_active:
        name: is_active
        description: "Team member's active status."
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      first_name: John
      last_name: Doe
      phone_number: '***********'
      is_active: true
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Team member updated successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "name": "John Doe",
              "email": "<EMAIL>",
              "phone": "***********",
              "role": "business-staff",
              "status": "active",
              "created_at": "2024-01-15T10:30:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/v1/business/team-members/{id}'
    metadata:
      groupName: 'Business Team'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Remove team member.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the team member.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Team member removed successfully"
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/business/team-members/{member}/assign-role'
    metadata:
      groupName: 'Business Team'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Assign role to team member (placeholder).'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      member:
        name: member
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      member: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 501
        content: |-
          {
            "success": false,
            "message": "Role assignment not implemented yet"
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/business/team-members/{member}/remove-role'
    metadata:
      groupName: 'Business Team'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Remove role from team member (placeholder).'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      member:
        name: member
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      member: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 501
        content: |-
          {
            "success": false,
            "message": "Role removal not implemented yet"
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/business/team-members/{member}/activate'
    metadata:
      groupName: 'Business Team'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Activate team member.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      member:
        name: member
        description: 'Team member ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      member: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Team member activated successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "name": "John Doe",
              "email": "<EMAIL>",
              "phone": "***********",
              "role": "staff",
              "is_active": true,
              "joined_at": "2024-01-15T10:30:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/business/team-members/{member}/deactivate'
    metadata:
      groupName: 'Business Team'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Deactivate team member.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      member:
        name: member
        description: 'Team member ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      member: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Team member deactivated successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "name": "John Doe",
              "email": "<EMAIL>",
              "phone": "***********",
              "role": "staff",
              "is_active": false,
              "joined_at": "2024-01-15T10:30:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/business/admins
    metadata:
      groupName: 'Business Team'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get business admins.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Business admins retrieved successfully",
            "data": [
              {
                "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                "name": "John Owner",
                "email": "<EMAIL>",
                "phone": "***********",
                "role": "business-owner",
                "is_active": true,
                "joined_at": "2024-01-01T00:00:00Z"
              },
              {
                "id": "019723aa-3202-70dd-a0c1-3565681dd88b",
                "name": "Jane Admin",
                "email": "<EMAIL>",
                "phone": "***********",
                "role": "business-admin",
                "is_active": true,
                "joined_at": "2024-01-15T10:30:00Z"
              }
            ]
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/business/assign-admin
    metadata:
      groupName: 'Business Team'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Assign admin role.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      user_id:
        name: user_id
        description: 'User ID to assign admin role.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      role:
        name: role
        description: 'Admin role to assign.'
        required: true
        example: business-admin
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      user_id: 019723aa-3202-70dd-a0c1-3565681dd87a
      role: business-admin
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Admin role assigned successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "name": "Jane Doe",
              "email": "<EMAIL>",
              "phone": "***********",
              "role": "business-admin",
              "is_active": true,
              "joined_at": "2024-01-15T10:30:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/business/transfer-admin
    metadata:
      groupName: 'Business Team'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Transfer ownership.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      new_owner_id:
        name: new_owner_id
        description: 'User ID of the new owner.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      confirmation_password:
        name: confirmation_password
        description: "Current owner's password for confirmation."
        required: true
        example: current_password
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      new_owner_id: 019723aa-3202-70dd-a0c1-3565681dd87a
      confirmation_password: current_password
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Ownership transferred successfully",
            "data": {
              "old_owner": {
                "id": "019723aa-3202-70dd-a0c1-3565681dd88a",
                "name": "John Owner",
                "role": "business-admin"
              },
              "new_owner": {
                "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                "name": "Jane Admin",
                "role": "business-owner"
              }
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/v1/business/admins/{user}/role'
    metadata:
      groupName: 'Business Team'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update admin role (placeholder).'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      user:
        name: user
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      user: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 501
        content: |-
          {
            "success": false,
            "message": "Admin role update not implemented yet"
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/v1/business/admins/{user}'
    metadata:
      groupName: 'Business Team'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Remove admin (placeholder).'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      user:
        name: user
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      user: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 501
        content: |-
          {
            "success": false,
            "message": "Admin removal not implemented yet"
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
