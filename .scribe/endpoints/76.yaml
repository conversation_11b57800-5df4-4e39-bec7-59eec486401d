name: 'QR Code Management'
description: ''
endpoints:
  -
    httpMethods:
      - POST
    uri: api/v1/business/qr-codes/menu
    metadata:
      groupName: 'QR Code Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Generate QR code for business menu.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      style:
        name: style
        description: 'The QR code style (classic, branded, minimal).'
        required: false
        example: branded
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      size:
        name: size
        description: 'The QR code size in pixels (200-800).'
        required: false
        example: 400
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      format:
        name: format
        description: 'The output format (png, svg, pdf).'
        required: false
        example: png
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      table_number:
        name: table_number
        description: 'Optional table number for restaurant context.'
        required: false
        example: T-12
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      campaign:
        name: campaign
        description: 'Optional campaign name for tracking.'
        required: false
        example: summer_menu
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      include_menu:
        name: include_menu
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      style: branded
      size: 400
      format: png
      table_number: T-12
      campaign: summer_menu
      include_menu: false
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/business/qr-codes/categories/{categoryId}'
    metadata:
      groupName: 'QR Code Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Generate QR code for specific category.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      categoryId:
        name: categoryId
        description: 'The category ID.'
        required: true
        example: 01234567-89ab-cdef-0123-456789abcdef
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      categoryId: 01234567-89ab-cdef-0123-456789abcdef
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      style:
        name: style
        description: ''
        required: false
        example: minimal
        type: string
        enumValues:
          - classic
          - branded
          - minimal
        exampleWasSpecified: false
        nullable: false
        custom: []
      size:
        name: size
        description: 'The QR code size in pixels (200-800).'
        required: false
        example: 300
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      format:
        name: format
        description: 'The output format (png, svg).'
        required: false
        example: png
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      table_number:
        name: table_number
        description: 'Optional table number.'
        required: false
        example: T-12
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      campaign:
        name: campaign
        description: 'Must not be greater than 50 characters.'
        required: false
        example: 'y'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      include_menu:
        name: include_menu
        description: ''
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      style: minimal
      size: 300
      format: png
      table_number: T-12
      campaign: 'y'
      include_menu: true
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/business/qr-codes/history
    metadata:
      groupName: 'QR Code Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get QR code history for business.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      page:
        name: page
        description: 'Page number for pagination.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'Items per page (max 50).'
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      type:
        name: type
        description: 'Filter by QR code type (menu, category).'
        required: false
        example: menu
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      page: 1
      per_page: 15
      type: menu
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:10.025594Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: c952f105-2937-417e-910d-3bb6b6ce133f
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-9xQWvNYPcdnjnHzl2r9dSw==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/business/qr-codes/analytics
    metadata:
      groupName: 'QR Code Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get QR code analytics.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      period:
        name: period
        description: 'Analytics period (7d, 30d, 90d).'
        required: false
        example: 30d
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      qr_code_id:
        name: qr_code_id
        description: 'Optional specific QR code ID.'
        required: false
        example: qr-menu-classic-123
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      period: 30d
      qr_code_id: qr-menu-classic-123
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:10.031827Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: e2b44539-a6e1-44d6-9942-40911eebcbed
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-bRAihg31eaphSVAhcktSow==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/business/qr-codes/{qrCodeId}/download'
    metadata:
      groupName: 'QR Code Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Download QR code in specified format.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      qrCodeId:
        name: qrCodeId
        description: 'The QR code ID.'
        required: true
        example: qr-menu-classic-123-1234567890
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      qrCodeId: qr-menu-classic-123-1234567890
    queryParameters:
      format:
        name: format
        description: 'Download format (png, svg, pdf).'
        required: false
        example: pdf
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      format: pdf
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-08T09:33:10.036622Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 5753073e-0aba-4cc1-89ff-b8152a72b14a
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-q8hbXWfX6L1EL9tMQAbDZA==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/business/qr-codes/bulk-generate
    metadata:
      groupName: 'QR Code Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Bulk generate QR codes for all categories.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      style:
        name: style
        description: ''
        required: false
        example: classic
        type: string
        enumValues:
          - classic
          - branded
          - minimal
        exampleWasSpecified: false
        nullable: false
        custom: []
      size:
        name: size
        description: 'The QR code size in pixels (200-800).'
        required: false
        example: 300
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      format:
        name: format
        description: 'The output format (png, svg).'
        required: false
        example: png
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      table_number:
        name: table_number
        description: 'Must not be greater than 20 characters.'
        required: false
        example: ngzmiyvdljnikhwa
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      campaign:
        name: campaign
        description: 'Must not be greater than 50 characters.'
        required: false
        example: 'y'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      include_menu:
        name: include_menu
        description: 'Whether to include main menu QR code.'
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      style: classic
      size: 300
      format: png
      table_number: ngzmiyvdljnikhwa
      campaign: 'y'
      include_menu: true
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
