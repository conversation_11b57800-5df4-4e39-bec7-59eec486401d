name: 'Provider Analytics'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1/provider/analytics/performance
    metadata:
      groupName: 'Provider Analytics'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get provider performance analytics.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      period:
        name: period
        description: 'Period for analytics (week, month, quarter, year).'
        required: false
        example: month
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      period: month
    bodyParameters:
      period:
        name: period
        description: ''
        required: false
        example: quarter
        type: string
        enumValues:
          - week
          - month
          - quarter
          - year
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      period: quarter
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Performance analytics retrieved successfully",
            "data": {
              "delivery_metrics": {
                "total_deliveries": 150,
                "completed_deliveries": 145,
                "cancelled_deliveries": 5,
                "success_rate": 96.67,
                "average_delivery_time": 28,
                "on_time_percentage": 92.41
              },
              "time_analysis": {
                "peak_hours": [
                  {"hour": 12, "deliveries": 25, "earnings": 7500},
                  {"hour": 19, "deliveries": 22, "earnings": 6600}
                ],
                "busiest_days": [
                  {"day": "Friday", "deliveries": 35, "earnings": 10500},
                  {"day": "Saturday", "deliveries": 32, "earnings": 9600}
                ]
              },
              "rating_analysis": {
                "average_rating": 4.7,
                "total_ratings": 142,
                "rating_distribution": {
                  "5": 85,
                  "4": 42,
                  "3": 12,
                  "2": 2,
                  "1": 1
                }
              }
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
