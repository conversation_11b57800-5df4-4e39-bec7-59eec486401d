<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\User\ChangePasswordRequest;
use App\Http\Requests\Api\V1\User\DeactivateAccountRequest;
use App\Http\Requests\Api\V1\User\UpdateProfileRequest;
use App\Http\Requests\Api\V1\User\UpdatePreferencesRequest;
use App\Services\User\UserProfileService;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Central User Profile Controller
 *
 * Handles user profile operations for CENTRAL DOMAIN users ONLY.
 * This includes customers and platform administrators.
 * Tenant users (business owners, staff, providers) should use TenantUserProfileController.
 */
class CentralUserProfileController extends Controller
{
    use ApiResponseTrait;

    public function __construct(
        private readonly UserProfileService $userProfileService
    ) {}

    /**
     * Get user profile for central users only.
     */
    public function show(Request $request): JsonResponse
    {
        $user = $request->user();

        // Ensure this is a central user (no tenant_id)
        if ($user->tenant_id !== null) {
            return $this->forbiddenResponse('This endpoint is for central users only. Please use the tenant domain.');
        }

        $profileResource = $this->userProfileService->getProfile($user, 'central');

        return $this->successResponse($profileResource, 'Central user profile retrieved successfully');
    }

    /**
     * Update user profile for central users only.
     */
    public function update(UpdateProfileRequest $request): JsonResponse
    {
        $user = $request->user();

        // Ensure this is a central user (no tenant_id)
        if ($user->tenant_id !== null) {
            return $this->forbiddenResponse('This endpoint is for central users only. Please use the tenant domain.');
        }

        $data = $request->validated();

        $updatedUser = $this->userProfileService->updateProfile($user, $data);
        $profileResource = $this->userProfileService->getProfile($updatedUser, 'central');

        return $this->successResponse($profileResource, 'Central user profile updated successfully');
    }

    /**
     * Change user password for central users only.
     */
    public function changePassword(ChangePasswordRequest $request): JsonResponse
    {
        $user = $request->user();

        // Ensure this is a central user (no tenant_id)
        if ($user->tenant_id !== null) {
            return $this->forbiddenResponse('This endpoint is for central users only. Please use the tenant domain.');
        }

        $data = $request->validated();

        $this->userProfileService->changePassword(
            $user,
            $data['current_password'],
            $data['new_password']
        );

        return $this->successResponse(null, 'Password changed successfully');
    }

    /**
     * Get user preferences for central users only.
     */
    public function getPreferences(Request $request): JsonResponse
    {
        $user = $request->user();

        // Ensure this is a central user (no tenant_id)
        if ($user->tenant_id !== null) {
            return $this->forbiddenResponse('This endpoint is for central users only. Please use the tenant domain.');
        }

        $preferences = $this->userProfileService->getUserPreferences($user);

        return $this->successResponse($preferences, 'Central user preferences retrieved successfully');
    }

    /**
     * Update user preferences for central users only.
     */
    public function updatePreferences(UpdatePreferencesRequest $request): JsonResponse
    {
        $user = $request->user();

        // Ensure this is a central user (no tenant_id)
        if ($user->tenant_id !== null) {
            return $this->forbiddenResponse('This endpoint is for central users only. Please use the tenant domain.');
        }

        $preferences = $request->validated();

        $updatedPreferences = $this->userProfileService->updateUserPreferences($user, $preferences);

        return $this->successResponse($updatedPreferences, 'Central user preferences updated successfully');
    }

    /**
     * Deactivate user account for central users only.
     */
    public function deactivate(DeactivateAccountRequest $request): JsonResponse
    {
        $user = $request->user();

        // Ensure this is a central user (no tenant_id)
        if ($user->tenant_id !== null) {
            return $this->forbiddenResponse('This endpoint is for central users only. Please use the tenant domain.');
        }

        $data = $request->validated();

        $this->userProfileService->deactivateAccount(
            $user,
            $data['password'],
            $data['reason'] ?? null
        );

        return $this->successResponse(null, 'Central user account deactivated successfully');
    }


}
