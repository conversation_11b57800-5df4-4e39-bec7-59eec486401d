<?php

declare(strict_types=1);

namespace App\Models\System;

use App\Models\Business\Business;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

/**
 * 
 *
 * @property string $id
 * @property string $key The API key hash or token
 * @property string|null $name e.g., "My Website Integration"
 * @property string|null $user_id
 * @property string|null $business_id
 * @property array<array-key, mixed>|null $abilities Specific permissions for this key (can override Bouncer)
 * @property \Illuminate\Support\Carbon|null $expires_at
 * @property \Illuminate\Support\Carbon|null $last_used_at
 * @property bool $is_active
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User\User|null $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ApiKey newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ApiKey newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ApiKey query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ApiKey whereAbilities($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ApiKey whereBusinessId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ApiKey whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ApiKey whereExpiresAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ApiKey whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ApiKey whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ApiKey whereKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ApiKey whereLastUsedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ApiKey whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ApiKey whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ApiKey whereUserId($value)
 * @property-read Business|null $business
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ApiKey active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ApiKey expired()
 * @mixin \Eloquent
 */
class ApiKey extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'name',
        'key_hash',
        'key_prefix',
        'user_id',
        'tenant_id',
        'type',
        'permissions',
        'rate_limits',
        'allowed_ips',
        'environment',
        'is_active',
        'last_used_at',
        'expires_at',
        'created_by',
    ];

    protected $casts = [
        'permissions' => 'array',
        'rate_limits' => 'array',
        'allowed_ips' => 'array',
        'expires_at' => 'datetime',
        'last_used_at' => 'datetime',
        'is_active' => 'boolean',
    ];

    /**
     * Get the user that owns the API key.
     *
     * @return BelongsTo<User,ApiKey>
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get the user who created the API key.
     *
     * @return BelongsTo<User,ApiKey>
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Generate a new API key.
     */
    public static function generateKey(): string
    {
        return 'dnx_'.Str::random(40);
    }

    /**
     * Create a new API key.
     */
    public static function createKey(array $data): array
    {
        $key = self::generateKey();
        $keyPrefix = substr($key, 0, 8);

        $apiKey = self::create([
            'name' => $data['name'],
            'key_hash' => Hash::make($key),
            'key_prefix' => $keyPrefix,
            'user_id' => $data['user_id'] ?? null,
            'tenant_id' => $data['tenant_id'] ?? null,
            'type' => $data['type'] ?? 'general',
            'permissions' => $data['permissions'] ?? null,
            'rate_limits' => $data['rate_limits'] ?? null,
            'allowed_ips' => $data['allowed_ips'] ?? null,
            'environment' => $data['environment'] ?? 'production',
            'expires_at' => $data['expires_at'] ?? null,
            'created_by' => $data['created_by'] ?? auth()->id(),
        ]);

        return [
            'api_key' => $apiKey,
            'key' => $key, // Only returned once
        ];
    }

    /**
     * Verify API key.
     */
    public static function verifyKey(string $key): ?self
    {
        $keyPrefix = substr($key, 0, 8);

        $apiKeys = self::where('is_active', true)
            ->where('key_prefix', $keyPrefix)
            ->get();

        foreach ($apiKeys as $apiKey) {
            if (Hash::check($key, $apiKey->key_hash)) {
                // Check if expired
                if ($apiKey->expires_at && $apiKey->expires_at->isPast()) {
                    return null;
                }

                // Update last used timestamp
                $apiKey->update(['last_used_at' => now()]);

                return $apiKey;
            }
        }

        return null;
    }

    /**
     * Check if API key has permission.
     */
    public function hasPermission(string $permission): bool
    {
        if (! $this->permissions) {
            return true; // No restrictions
        }

        return in_array($permission, $this->permissions);
    }

    /**
     * Check if API key has ability (backward compatibility).
     */
    public function hasAbility(string $ability): bool
    {
        return $this->hasPermission($ability);
    }

    /**
     * Check if API key is expired.
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * Check if API key is valid.
     */
    public function isValid(): bool
    {
        return $this->is_active && ! $this->isExpired();
    }

    /**
     * Get masked key for display.
     */
    public function getMaskedKey(): string
    {
        return $this->key_prefix.str_repeat('*', 36);
    }

    /**
     * Scope for active keys.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for expired keys.
     */
    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<', now());
    }

    /**
     * Revoke API key.
     */
    public function revoke(): bool
    {
        return $this->update(['is_active' => false]);
    }

    /**
     * Get available permissions.
     */
    public static function getAvailablePermissions(): array
    {
        return [
            'orders.read',
            'orders.write',
            'products.read',
            'products.write',
            'businesses.read',
            'businesses.write',
            'deliveries.read',
            'deliveries.write',
            'analytics.read',
            'webhooks.receive',
            'api.admin',
            'payments.read',
            'payments.write',
            'customers.read',
            'inventory.read',
            'inventory.write',
            'drivers.read',
            'drivers.write',
            'vehicles.read',
            'vehicles.write',
            'routes.read',
            'routes.write',
            'earnings.read',
            'notifications.send',
            'sms.send',
            'reports.generate',
        ];
    }

    /**
     * Get available abilities (backward compatibility).
     */
    public static function getAvailableAbilities(): array
    {
        return self::getAvailablePermissions();
    }
}
