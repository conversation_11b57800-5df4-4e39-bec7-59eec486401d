<?php

declare(strict_types=1);

namespace App\Models\System;

use App\Enums\System\ContentStatus;
use App\Enums\System\ContentType;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Content Model
 * 
 * Manages CMS content across the platform including pages, announcements,
 * legal documents, FAQs, and other content types.
 *
 * @property string $id
 * @property string $title
 * @property string $slug
 * @property string|null $excerpt
 * @property string $content
 * @property ContentType $type
 * @property ContentStatus $status
 * @property string|null $meta_title
 * @property string|null $meta_description
 * @property array|null $meta_keywords
 * @property string|null $featured_image
 * @property array|null $settings
 * @property string|null $author_id
 * @property string|null $published_by
 * @property \Illuminate\Support\Carbon|null $published_at
 * @property \Illuminate\Support\Carbon|null $expires_at
 * @property int $view_count
 * @property bool $is_featured
 * @property bool $allow_comments
 * @property string|null $language
 * @property int $sort_order
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read User|null $author
 * @property-read User|null $publisher
 * @property-read \Illuminate\Database\Eloquent\Collection<int, ContentVersion> $versions
 * @property-read int $reading_time
 * @property-read string $url
 * @property-read int|null $versions_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Content featured()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Content inLanguage(string $language)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Content newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Content newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Content ofType(\App\Enums\System\ContentType $type)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Content onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Content published()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Content query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Content withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Content withoutTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Content whereAllowComments($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Content whereAuthorId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Content whereContent($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Content whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Content whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Content whereExcerpt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Content whereExpiresAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Content whereFeaturedImage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Content whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Content whereIsFeatured($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Content whereLanguage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Content whereMetaDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Content whereMetaKeywords($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Content whereMetaTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Content wherePublishedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Content wherePublishedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Content whereSettings($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Content whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Content whereSortOrder($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Content whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Content whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Content whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Content whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Content whereViewCount($value)
 * @mixin \Eloquent
 */
class Content extends Model
{
    use HasFactory, HasUuids, SoftDeletes;

    protected $fillable = [
        'title',
        'slug',
        'excerpt',
        'content',
        'type',
        'status',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'featured_image',
        'settings',
        'author_id',
        'published_by',
        'published_at',
        'expires_at',
        'view_count',
        'is_featured',
        'allow_comments',
        'language',
        'sort_order',
    ];

    protected $casts = [
        'type' => ContentType::class,
        'status' => ContentStatus::class,
        'meta_keywords' => 'array',
        'settings' => 'array',
        'published_at' => 'datetime',
        'expires_at' => 'datetime',
        'view_count' => 'integer',
        'is_featured' => 'boolean',
        'allow_comments' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Get the author of the content.
     */
    public function author(): BelongsTo
    {
        return $this->belongsTo(User::class, 'author_id');
    }

    /**
     * Get the user who published the content.
     */
    public function publisher(): BelongsTo
    {
        return $this->belongsTo(User::class, 'published_by');
    }

    /**
     * Get all versions of this content.
     */
    public function versions(): HasMany
    {
        return $this->hasMany(ContentVersion::class);
    }

    /**
     * Scope for published content.
     */
    public function scopePublished($query)
    {
        return $query->where('status', ContentStatus::PUBLISHED)
            ->where(function ($q) {
                $q->whereNull('published_at')
                    ->orWhere('published_at', '<=', now());
            })
            ->where(function ($q) {
                $q->whereNull('expires_at')
                    ->orWhere('expires_at', '>', now());
            });
    }

    /**
     * Scope for featured content.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope by content type.
     */
    public function scopeOfType($query, ContentType $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope by language.
     */
    public function scopeInLanguage($query, string $language)
    {
        return $query->where('language', $language);
    }

    /**
     * Check if content is published.
     */
    public function isPublished(): bool
    {
        return $this->status === ContentStatus::PUBLISHED
            && ($this->published_at === null || $this->published_at <= now())
            && ($this->expires_at === null || $this->expires_at > now());
    }

    /**
     * Check if content is expired.
     */
    public function isExpired(): bool
    {
        return $this->expires_at !== null && $this->expires_at <= now();
    }

    /**
     * Increment view count.
     */
    public function incrementViewCount(): void
    {
        $this->increment('view_count');
    }

    /**
     * Get the URL for this content.
     */
    public function getUrlAttribute(): string
    {
        return match ($this->type) {
            ContentType::PAGE => "/pages/{$this->slug}",
            ContentType::ANNOUNCEMENT => "/announcements/{$this->slug}",
            ContentType::FAQ => "/help/faq#{$this->slug}",
            ContentType::LEGAL => "/legal/{$this->slug}",
            ContentType::HELP => "/help/{$this->slug}",
            default => "/content/{$this->slug}",
        };
    }

    /**
     * Get content excerpt or generate from content.
     */
    public function getExcerptAttribute($value): string
    {
        if ($value) {
            return $value;
        }

        // Generate excerpt from content (first 160 characters)
        return str($this->content)
            ->stripTags()
            ->limit(160)
            ->toString();
    }

    /**
     * Get reading time estimate in minutes.
     */
    public function getReadingTimeAttribute(): int
    {
        $wordCount = str_word_count(strip_tags($this->content));

        return max(1, (int) ceil($wordCount / 200)); // Average reading speed: 200 words/minute
    }

    /**
     * Get content settings with defaults.
     */
    public function getSettingsAttribute($value): array
    {
        $settings = $value ? json_decode($value, true) : [];

        return array_merge([
            'show_author' => true,
            'show_date' => true,
            'show_reading_time' => false,
            'enable_sharing' => true,
            'enable_print' => true,
            'custom_css' => null,
            'custom_js' => null,
        ], $settings);
    }
}
