<?php

declare(strict_types=1);

namespace App\Models\System;

use App\Models\User\User;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Content Version Model
 * 
 * Tracks versions of content for audit trail and rollback capabilities.
 *
 * @property string $id
 * @property string $content_id
 * @property int $version_number
 * @property string $title
 * @property string|null $excerpt
 * @property string $content
 * @property array|null $changes
 * @property string|null $change_summary
 * @property string $created_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read Content $content
 * @property-read User $createdBy
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContentVersion newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContentVersion newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContentVersion query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContentVersion whereChangeSummary($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContentVersion whereChanges($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContentVersion whereContent($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContentVersion whereContentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContentVersion whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContentVersion whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContentVersion whereExcerpt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContentVersion whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContentVersion whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContentVersion whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ContentVersion whereVersionNumber($value)
 * @mixin \Eloquent
 */
class ContentVersion extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'content_id',
        'version_number',
        'title',
        'excerpt',
        'content',
        'changes',
        'change_summary',
        'created_by',
    ];

    protected $casts = [
        'version_number' => 'integer',
        'changes' => 'array',
    ];

    /**
     * Get the content this version belongs to.
     */
    public function content(): BelongsTo
    {
        return $this->belongsTo(Content::class);
    }

    /**
     * Get the user who created this version.
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Create a new version from content.
     */
    public static function createFromContent(Content $content, ?string $changeSummary = null): self
    {
        $latestVersion = $content->versions()->latest('version_number')->first();
        $nextVersionNumber = $latestVersion ? $latestVersion->version_number + 1 : 1;

        return self::create([
            'content_id' => $content->id,
            'version_number' => $nextVersionNumber,
            'title' => $content->title,
            'excerpt' => $content->excerpt,
            'content' => $content->content,
            'changes' => self::calculateChanges($latestVersion, $content),
            'change_summary' => $changeSummary,
            'created_by' => auth()->id(),
        ]);
    }

    /**
     * Calculate changes between versions.
     */
    private static function calculateChanges(?self $previousVersion, Content $currentContent): array
    {
        if (! $previousVersion) {
            return ['type' => 'created'];
        }

        $changes = [];

        if ($previousVersion->title !== $currentContent->title) {
            $changes['title'] = [
                'from' => $previousVersion->title,
                'to' => $currentContent->title,
            ];
        }

        if ($previousVersion->excerpt !== $currentContent->excerpt) {
            $changes['excerpt'] = [
                'from' => $previousVersion->excerpt,
                'to' => $currentContent->excerpt,
            ];
        }

        if ($previousVersion->content !== $currentContent->content) {
            $changes['content'] = [
                'changed' => true,
                'length_change' => strlen($currentContent->content) - strlen($previousVersion->content),
            ];
        }

        return $changes;
    }

    /**
     * Get a summary of changes in this version.
     */
    public function getChangesSummary(): string
    {
        if ($this->change_summary) {
            return $this->change_summary;
        }

        if (! $this->changes) {
            return 'No changes recorded';
        }

        $changes = $this->changes;
        $summary = [];

        if (isset($changes['type']) && $changes['type'] === 'created') {
            return 'Content created';
        }

        if (isset($changes['title'])) {
            $summary[] = 'Title updated';
        }

        if (isset($changes['excerpt'])) {
            $summary[] = 'Excerpt updated';
        }

        if (isset($changes['content'])) {
            $lengthChange = $changes['content']['length_change'] ?? 0;
            if ($lengthChange > 0) {
                $summary[] = "Content expanded (+{$lengthChange} characters)";
            } elseif ($lengthChange < 0) {
                $summary[] = 'Content reduced ('.abs($lengthChange).' characters)';
            } else {
                $summary[] = 'Content modified';
            }
        }

        return empty($summary) ? 'Minor changes' : implode(', ', $summary);
    }
}
