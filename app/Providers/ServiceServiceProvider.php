<?php

declare(strict_types=1);

namespace App\Providers;

use App\Contracts\PaymentServiceInterface;
use App\Contracts\SocialAuthServiceInterface;
use App\Notifications\Channels\VerificationSmsChannel;
use App\Services\Communication\HybridSmsService;
use App\Services\Delivery\DemandForecastingService;
use App\Services\Delivery\DynamicPricingService;
use App\Services\Financial\PaystackService;
use App\Services\Integration\GoogleMapsService;
use App\Services\Integration\ImageStorageService;
use App\Services\System\LoggingService;
use App\Services\User\SocialAuthService;
use Illuminate\Support\ServiceProvider;

/**
 * Service Service Provider
 *
 * Binds service interfaces to implementations.
 * Switch between dummy and real implementations based on environment.
 */
class ServiceServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Email delivery is now handled by <PERSON><PERSON>'s built-in mail system
        // Configure MAIL_MAILER in .env (smtp, sendmail, mailgun, etc.)

        // Hybrid SMS Service (Twilio Verify API + Laravel Notification Channel)
        $this->app->singleton(HybridSmsService::class);

        // Verification SMS Channel (uses Twilio Verify API for OTP codes)
        $this->app->bind('verification_sms', VerificationSmsChannel::class);

        // Social Auth Service
        $this->app->bind(SocialAuthServiceInterface::class, SocialAuthService::class);

        // Payment Service (Paystack)
        $this->app->bind(PaymentServiceInterface::class, function ($app) {
            if (config('services.paystack.enabled', false)) {
                return new PaystackService;
            }

            // Return dummy implementation for development
            return new class implements PaymentServiceInterface
            {
                public function initializePayment(float $amount, array $metadata): array
                {
                    return [
                        'reference' => 'dummy_ref_'.uniqid(),
                        'authorization_url' => 'https://dummy-payment.example.com',
                        'access_code' => 'dummy_access_code',
                    ];
                }

                public function verifyPayment(string $reference): bool
                {
                    return true; // Always successful in dummy
                }

                public function processRefund(string $reference, float $amount): bool
                {
                    return true;
                }

                public function getPaymentStatus(string $reference): string
                {
                    return 'success';
                }

                public function getPaymentMethods(): array
                {
                    return ['card', 'bank_transfer', 'ussd'];
                }

                public function calculateFee(float $amount): float
                {
                    return $amount * 0.015; // 1.5% fee
                }
            };
        });

        // SMS Notification Channels are auto-registered by laravel-notification-channels/twilio package

        // Logging Service
        $this->app->singleton(LoggingService::class);

        // Google Maps Service
        $this->app->singleton(GoogleMapsService::class, function ($app) {
            return new GoogleMapsService($app->make(\App\Services\System\LoggingService::class));
        });

        // Image Storage Service
        $this->app->singleton(ImageStorageService::class, function ($app) {
            return new ImageStorageService($app->make(\App\Services\System\LoggingService::class));
        });

        // Demand Forecasting Service
        $this->app->singleton(DemandForecastingService::class, function ($app) {
            return new DemandForecastingService($app->make(\App\Services\System\LoggingService::class));
        });

        // Dynamic Pricing Service
        $this->app->singleton(DynamicPricingService::class, function ($app) {
            return new DynamicPricingService(
                $app->make(\App\Services\System\LoggingService::class),
                $app->make(DemandForecastingService::class)
            );
        });

        // User Profile Service
        $this->app->singleton(\App\Services\User\UserProfileService::class, function ($app) {
            return new \App\Services\User\UserProfileService(
                $app->make(\App\Services\System\LoggingService::class)
            );
        });

        // Central User Profile Controller
        $this->app->singleton(\App\Http\Controllers\Api\V1\Central\CentralUserProfileController::class, function ($app) {
            return new \App\Http\Controllers\Api\V1\Central\CentralUserProfileController(
                $app->make(\App\Services\User\UserProfileService::class)
            );
        });

        // Tenant User Profile Controller
        $this->app->singleton(\App\Http\Controllers\Api\V1\Tenant\TenantUserProfileController::class, function ($app) {
            return new \App\Http\Controllers\Api\V1\Tenant\TenantUserProfileController(
                $app->make(\App\Services\User\UserProfileService::class)
            );
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
