<?php

declare(strict_types=1);

namespace App\Services\User;

use App\Exceptions\BusinessLogicException;
use App\Http\Resources\Api\V1\UserProfileResource;
use App\Models\User\Address;
use App\Models\User\CustomerProfile;
use App\Models\User\User;
use App\Models\User\UserPreference;
use App\Services\System\LoggingService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

/**
 * User Profile Service
 *
 * Handles user profile management operations:
 * - Profile updates
 * - Password changes
 * - User preferences
 * - Account deactivation
 */
class UserProfileService
{
    public function __construct(
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get user profile data for any context.
     */
    public function getProfile(User $user, string $context = null): UserProfileResource
    {
        // Load necessary relationships
        $user->load([
            'roles',
            'abilities',
            'addresses',
            'userPreferences',
            'customerProfile',
        ]);

        $actualContext = $context ?? ($user->tenant_id ? 'tenant' : 'central');

        $this->loggingService->logInfo('User profile retrieved', [
            'user_id' => $user->id,
            'user_type' => $user->getUserType(),
            'context' => $actualContext,
        ]);

        return new UserProfileResource($user);
    }

    /**
     * Update user profile information.
     */
    public function updateProfile(User $user, array $data): User
    {
        return DB::transaction(function () use ($user, $data) {
            // Extract address data if present
            $addressData = $data['address'] ?? null;
            unset($data['address']);

            // Extract profile-specific data
            $profileData = [];
            $profileFields = ['avatar_url', 'bio', 'date_of_birth', 'gender', 'business_role', 'department'];

            foreach ($profileFields as $field) {
                if (isset($data[$field])) {
                    $profileData[$field] = $data[$field];
                    unset($data[$field]);
                }
            }

            // Update user basic information
            $user->update($data);

            // Update or create customer profile if profile data exists
            if (! empty($profileData)) {
                $this->updateCustomerProfile($user, $profileData);
            }

            // Update address if provided
            if ($addressData) {
                $this->updateUserAddress($user, $addressData);
            }

            return $user->fresh();
        });
    }

    /**
     * Change user password.
     */
    public function changePassword(User $user, string $currentPassword, string $newPassword): bool
    {
        // Verify current password
        if (! Hash::check($currentPassword, $user->password)) {
            throw new BusinessLogicException('Current password is incorrect');
        }

        // Check if new password is different from current
        if (Hash::check($newPassword, $user->password)) {
            throw new BusinessLogicException('New password must be different from current password');
        }

        // Update password
        $user->update([
            'password' => Hash::make($newPassword),
        ]);

        // Revoke all existing tokens except current one
        $currentToken = $user->currentAccessToken();
        $user->tokens()->where('id', '!=', $currentToken?->id)->delete();

        return true;
    }

    /**
     * Get user preferences.
     */
    public function getUserPreferences(User $user): array
    {
        $preferences = $user->userPreferences()
            ->get()
            ->mapWithKeys(function ($preference) {
                return [$preference->preference_key => $this->parsePreferenceValue($preference->preference_value)];
            })
            ->toArray();

        // Return structured preferences with defaults
        return [
            'notifications' => [
                'email_enabled' => $preferences['notifications.email_enabled'] ?? true,
                'sms_enabled' => $preferences['notifications.sms_enabled'] ?? true,
                'push_enabled' => $preferences['notifications.push_enabled'] ?? true,
                'marketing_enabled' => $preferences['notifications.marketing_enabled'] ?? false,
                'order_updates' => $preferences['notifications.order_updates'] ?? true,
                'delivery_updates' => $preferences['notifications.delivery_updates'] ?? true,
                'promotional_offers' => $preferences['notifications.promotional_offers'] ?? false,
                'security_alerts' => $preferences['notifications.security_alerts'] ?? true,
            ],
            'communication' => [
                'preferred_language' => $preferences['communication.preferred_language'] ?? 'en',
                'preferred_contact_method' => $preferences['communication.preferred_contact_method'] ?? 'email',
                'contact_time_start' => $preferences['communication.contact_time_start'] ?? '09:00',
                'contact_time_end' => $preferences['communication.contact_time_end'] ?? '18:00',
            ],
            'privacy' => [
                'profile_visibility' => $preferences['privacy.profile_visibility'] ?? 'public',
                'location_sharing' => $preferences['privacy.location_sharing'] ?? true,
                'activity_tracking' => $preferences['privacy.activity_tracking'] ?? true,
                'data_analytics' => $preferences['privacy.data_analytics'] ?? true,
            ],
            'app' => [
                'theme' => $preferences['app.theme'] ?? 'auto',
                'currency' => $preferences['app.currency'] ?? 'NGN',
                'distance_unit' => $preferences['app.distance_unit'] ?? 'km',
                'temperature_unit' => $preferences['app.temperature_unit'] ?? 'celsius',
            ],

        ];
    }

    /**
     * Update user preferences.
     */
    public function updateUserPreferences(User $user, array $preferences): array
    {
        // Filter out business and delivery settings - these should be handled by dedicated services
        $allowedCategories = ['notifications', 'communication', 'privacy', 'app'];
        $filteredPreferences = array_intersect_key($preferences, array_flip($allowedCategories));

        DB::transaction(function () use ($user, $filteredPreferences) {
            foreach ($filteredPreferences as $category => $categoryPrefs) {
                if (is_array($categoryPrefs)) {
                    foreach ($categoryPrefs as $key => $value) {
                        $preferenceKey = "{$category}.{$key}";

                        UserPreference::updateOrCreate(
                            [
                                'user_id' => $user->id,
                                'tenant_id' => $user->tenant_id,
                                'preference_key' => $preferenceKey,
                            ],
                            [
                                'preference_value' => $this->formatPreferenceValue($value),
                            ]
                        );
                    }
                }
            }
        });

        return $this->getUserPreferences($user);
    }

    /**
     * Deactivate user account.
     */
    public function deactivateAccount(User $user, string $password, ?string $reason = null): bool
    {
        // Verify password
        if (! Hash::check($password, $user->password)) {
            throw new BusinessLogicException('Password is incorrect');
        }

        // Platform admins cannot deactivate themselves
        if ($user->isPlatformAdmin()) {
            throw new BusinessLogicException('Platform administrators cannot deactivate their own accounts');
        }

        return DB::transaction(function () use ($user, $reason) {
            // Deactivate user
            $user->update(['is_active' => false]);

            // Revoke all tokens
            $user->tokens()->delete();

            // Log deactivation reason if provided
            if ($reason) {
                $this->loggingService->logAuth('account_deactivation_reason', [
                    'user_id' => $user->id,
                    'reason' => $reason,
                ], $user->id);
            }

            return true;
        });
    }

    /**
     * Update or create customer profile.
     */
    private function updateCustomerProfile(User $user, array $profileData): void
    {
        CustomerProfile::updateOrCreate(
            ['user_id' => $user->id],
            $profileData
        );
    }

    /**
     * Update user address.
     */
    private function updateUserAddress(User $user, array $addressData): void
    {
        // If this is set as default, unset other default addresses
        if ($addressData['is_default'] ?? false) {
            $user->addresses()->update(['is_default' => false]);
        }

        // Create or update address
        $user->addresses()->create(array_merge($addressData, [
            'type' => 'home', // Default type
        ]));
    }

    /**
     * Parse preference value from storage.
     */
    private function parsePreferenceValue(string $value): mixed
    {
        // Try to decode as JSON first
        $decoded = json_decode($value, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            return $decoded;
        }

        // Handle boolean strings
        if ($value === 'true') {
            return true;
        }
        if ($value === 'false') {
            return false;
        }

        // Handle numeric strings
        if (is_numeric($value)) {
            return str_contains($value, '.') ? (float) $value : (int) $value;
        }

        return $value;
    }

    /**
     * Format preference value for storage.
     */
    private function formatPreferenceValue(mixed $value): string
    {
        if (is_array($value) || is_object($value)) {
            return json_encode($value);
        }

        if (is_bool($value)) {
            return $value ? 'true' : 'false';
        }

        return (string) $value;
    }
}
