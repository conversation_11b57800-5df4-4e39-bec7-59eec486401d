<?php

declare(strict_types=1);

namespace Database\Seeders\Dev;

use App\Models\Business\Business;
use App\Models\Delivery\DeliveryProvider;
use App\Models\System\ApiKey;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class ApiKeySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Starting ApiKeySeeder...');

        // Check for required data
        if (! $this->hasRequiredData()) {
            return;
        }

        // Create API keys for businesses
        $this->createBusinessApiKeys();

        // Create API keys for delivery providers
        $this->createDeliveryProviderApiKeys();

        // Create API keys for platform integrations
        $this->createPlatformApiKeys();

        $this->command->info('ApiKeySeeder completed successfully.');
    }

    /**
     * Check if required data exists.
     */
    private function hasRequiredData(): bool
    {
        $businesses = Business::count();
        $providers = DeliveryProvider::count();

        if ($businesses === 0 && $providers === 0) {
            $this->command->warn('No businesses or delivery providers found. Skipping ApiKeySeeder.');

            return false;
        }

        return true;
    }

    /**
     * Create API keys for businesses.
     */
    private function createBusinessApiKeys(): void
    {
        $businesses = Business::all();
        $keysCreated = 0;

        foreach ($businesses as $business) {
            // 60% chance a business has API keys
            if (fake()->boolean(60)) {
                $keyCount = fake()->numberBetween(1, 3);

                for ($i = 0; $i < $keyCount; $i++) {
                    $createdAt = fake()->dateTimeBetween('-6 months', 'now');

                    $key = $this->generateApiKey('biz');
                    ApiKey::create([
                        'name' => $this->generateBusinessKeyName($i),
                        'key_hash' => \Illuminate\Support\Facades\Hash::make($key),
                        'key_prefix' => substr($key, 0, 8),
                        'user_id' => $business->user_id,
                        'tenant_id' => $business->tenant_id,
                        'type' => 'business',
                        'permissions' => $this->getBusinessPermissions(),
                        'rate_limits' => ['requests_per_minute' => $this->getBusinessRateLimit()],
                        'allowed_ips' => $this->getAllowedIps(),
                        'environment' => 'production',
                        'is_active' => $this->getKeyStatus($createdAt),
                        'expires_at' => $this->getExpirationDate($createdAt),
                        'last_used_at' => $this->getLastUsedAt($createdAt),
                        'created_by' => $business->user_id,
                        'created_at' => $createdAt,
                        'updated_at' => fake()->dateTimeBetween($createdAt, 'now'),
                    ]);

                    $keysCreated++;
                }
            }
        }

        $this->command->info("Created {$keysCreated} business API keys.");
    }

    /**
     * Create API keys for delivery providers.
     */
    private function createDeliveryProviderApiKeys(): void
    {
        $providers = DeliveryProvider::all();
        $keysCreated = 0;

        foreach ($providers as $provider) {
            // 80% chance a delivery provider has API keys (they need them for integrations)
            if (fake()->boolean(80)) {
                $keyCount = fake()->numberBetween(1, 2);

                for ($i = 0; $i < $keyCount; $i++) {
                    $createdAt = fake()->dateTimeBetween('-6 months', 'now');

                    $key = $this->generateApiKey('prv');
                    ApiKey::create([
                        'name' => $this->generateProviderKeyName($i),
                        'key_hash' => \Illuminate\Support\Facades\Hash::make($key),
                        'key_prefix' => substr($key, 0, 8),
                        'user_id' => $provider->user_id,
                        'tenant_id' => $provider->tenant_id,
                        'type' => 'provider',
                        'permissions' => $this->getProviderPermissions(),
                        'rate_limits' => ['requests_per_minute' => $this->getProviderRateLimit()],
                        'allowed_ips' => $this->getAllowedIps(),
                        'environment' => 'production',
                        'is_active' => $this->getKeyStatus($createdAt),
                        'expires_at' => $this->getExpirationDate($createdAt),
                        'last_used_at' => $this->getLastUsedAt($createdAt),
                        'created_by' => $provider->user_id,
                        'created_at' => $createdAt,
                        'updated_at' => fake()->dateTimeBetween($createdAt, 'now'),
                    ]);

                    $keysCreated++;
                }
            }
        }

        $this->command->info("Created {$keysCreated} delivery provider API keys.");
    }

    /**
     * Create platform API keys for integrations.
     */
    private function createPlatformApiKeys(): void
    {
        $platformKeys = [
            [
                'name' => 'Paystack Integration',
                'permissions' => ['payments.read', 'payments.write', 'webhooks.receive'],
                'rate_limit' => 1000,
            ],
            [
                'name' => 'SMS Service Integration',
                'permissions' => ['notifications.send', 'sms.send'],
                'rate_limit' => 500,
            ],
            [
                'name' => 'Analytics Service',
                'permissions' => ['analytics.read', 'reports.generate'],
                'rate_limit' => 200,
            ],
            [
                'name' => 'Mobile App API',
                'permissions' => ['orders.read', 'orders.write', 'users.read', 'deliveries.read'],
                'rate_limit' => 2000,
            ],
        ];

        $keysCreated = 0;

        foreach ($platformKeys as $keyData) {
            $createdAt = fake()->dateTimeBetween('-1 year', '-6 months');

            $key = $this->generateApiKey('plt');
            ApiKey::create([
                'name' => $keyData['name'],
                'key_hash' => \Illuminate\Support\Facades\Hash::make($key),
                'key_prefix' => substr($key, 0, 8),
                'user_id' => null, // Platform keys don't belong to specific users
                'tenant_id' => null,
                'type' => 'platform',
                'permissions' => $keyData['permissions'],
                'rate_limits' => ['requests_per_minute' => $keyData['rate_limit']],
                'allowed_ips' => null,
                'environment' => 'production',
                'is_active' => true,
                'expires_at' => now()->addYear(), // Platform keys have longer expiration
                'last_used_at' => fake()->dateTimeBetween($createdAt, 'now'),
                'created_by' => null,
                'created_at' => $createdAt,
                'updated_at' => fake()->dateTimeBetween($createdAt, 'now'),
            ]);

            $keysCreated++;
        }

        $this->command->info("Created {$keysCreated} platform API keys.");
    }

    /**
     * Generate business API key name.
     */
    private function generateBusinessKeyName(int $index): string
    {
        $names = [
            'Production API Key',
            'Development API Key',
            'Mobile App Integration',
            'Website Integration',
            'POS System Integration',
            'Third-party Integration',
        ];

        return $names[$index] ?? 'API Key '.($index + 1);
    }

    /**
     * Generate provider API key name.
     */
    private function generateProviderKeyName(int $index): string
    {
        $names = [
            'Driver Mobile App',
            'Dispatch System',
            'Fleet Management',
            'Route Optimization',
        ];

        return $names[$index] ?? 'Provider API Key '.($index + 1);
    }

    /**
     * Generate API key.
     */
    private function generateApiKey(string $prefix): string
    {
        return $prefix.'_'.Str::random(32);
    }

    /**
     * Get business permissions.
     */
    private function getBusinessPermissions(): array
    {
        $allPermissions = [
            'orders.read',
            'orders.write',
            'products.read',
            'products.write',
            'customers.read',
            'analytics.read',
            'payments.read',
            'deliveries.read',
            'inventory.read',
            'inventory.write',
        ];

        // Return 4-8 random permissions
        return fake()->randomElements($allPermissions, fake()->numberBetween(4, 8));
    }

    /**
     * Get provider permissions.
     */
    private function getProviderPermissions(): array
    {
        $allPermissions = [
            'deliveries.read',
            'deliveries.write',
            'drivers.read',
            'drivers.write',
            'vehicles.read',
            'vehicles.write',
            'routes.read',
            'routes.write',
            'earnings.read',
            'analytics.read',
        ];

        // Return 3-6 random permissions
        return fake()->randomElements($allPermissions, fake()->numberBetween(3, 6));
    }

    /**
     * Get business rate limit.
     */
    private function getBusinessRateLimit(): int
    {
        return fake()->randomElement([100, 200, 500, 1000]);
    }

    /**
     * Get provider rate limit.
     */
    private function getProviderRateLimit(): int
    {
        return fake()->randomElement([200, 500, 1000, 1500]);
    }

    /**
     * Get allowed IPs.
     */
    private function getAllowedIps(): ?array
    {
        // 40% chance of having IP restrictions
        if (fake()->boolean(40)) {
            return [
                fake()->ipv4(),
                fake()->ipv4(),
            ];
        }

        return null;
    }

    /**
     * Get key status.
     */
    private function getKeyStatus(\DateTime $createdAt): bool
    {
        $daysSinceCreated = (new \DateTime)->diff($createdAt)->days;

        if ($daysSinceCreated > 180) {
            return fake()->boolean(70); // Older keys might be deactivated
        }

        return fake()->boolean(90); // Newer keys are mostly active
    }

    /**
     * Get expiration date.
     */
    private function getExpirationDate(\DateTime $createdAt): ?\DateTime
    {
        // 70% chance of having an expiration date
        if (fake()->boolean(70)) {
            return (clone $createdAt)->modify('+1 year');
        }

        return null;
    }

    /**
     * Get last used timestamp.
     */
    private function getLastUsedAt(\DateTime $createdAt): ?\DateTime
    {
        // 80% chance the key has been used
        if (fake()->boolean(80)) {
            return fake()->dateTimeBetween($createdAt, 'now');
        }

        return null;
    }

    /**
     * Get usage count.
     */
    private function getUsageCount(\DateTime $createdAt): int
    {
        $daysSinceCreated = (new \DateTime)->diff($createdAt)->days;

        if ($daysSinceCreated > 30) {
            return fake()->numberBetween(100, 10000);
        } elseif ($daysSinceCreated > 7) {
            return fake()->numberBetween(10, 1000);
        } else {
            return fake()->numberBetween(0, 100);
        }
    }
}
