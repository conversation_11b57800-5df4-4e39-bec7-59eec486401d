<?php

declare(strict_types=1);

use App\Http\Controllers\Api\V1\Auth\ApiAuthController;
use App\Http\Controllers\Api\V1\Shared\NotificationController;
use App\Http\Controllers\Api\V1\Tenant\TenantUserProfileController;
use App\Http\Controllers\Api\V1\Tenant\Business\BusinessAnalyticsController;
use App\Http\Controllers\Api\V1\Tenant\Business\BusinessDashboardController;
use App\Http\Controllers\Api\V1\Tenant\Business\BusinessProfileController;
use App\Http\Controllers\Api\V1\Tenant\Business\BusinessSettingsController;
use App\Http\Controllers\Api\V1\Tenant\Business\BusinessTeamController;
use App\Http\Controllers\Api\V1\Tenant\Business\BusinessCategoryController;
use App\Http\Controllers\Api\V1\Tenant\Business\BusinessDeliveryController;
use App\Http\Controllers\Api\V1\Tenant\Business\BusinessOrderController;
use App\Http\Controllers\Api\V1\Tenant\Business\BusinessProductController;
use App\Http\Controllers\Api\V1\Tenant\Business\BusinessDeliveryAssignmentController;
use App\Http\Controllers\Api\V1\Tenant\Business\BusinessPayoutController;
use App\Http\Controllers\Api\V1\Tenant\Business\BusinessPickupSlotController;
use App\Http\Controllers\Api\V1\Tenant\Business\BusinessProductCatalogController;
use App\Http\Controllers\Api\V1\Tenant\Business\BusinessProductCollectionController;
use App\Http\Controllers\Api\V1\Tenant\Business\BusinessQrCodeController;
use App\Http\Controllers\Api\V1\Tenant\Business\BusinessWhatsAppConfigController;
use App\Http\Controllers\Api\V1\Tenant\Provider\ProviderRealTimeTrackingController;
use App\Http\Controllers\Api\V1\Tenant\Provider\ProviderDeliveryController;
use App\Http\Controllers\Api\V1\Tenant\Provider\ProviderDeliveryRequestController;
use App\Http\Controllers\Api\V1\Tenant\Provider\ProviderDriverController;
use App\Http\Controllers\Api\V1\Tenant\Provider\ProviderDriverPerformanceController;
use App\Http\Controllers\Api\V1\Tenant\Provider\ProviderPayoutController;
use App\Http\Controllers\Api\V1\Tenant\Provider\ProviderAnalyticsController;
use App\Http\Controllers\Api\V1\Tenant\Provider\ProviderDashboardController;
use App\Http\Controllers\Api\V1\Tenant\Provider\ProviderProfileController;
use App\Http\Controllers\Api\V1\Tenant\Provider\ProviderSettingsController;
use App\Http\Controllers\Api\V1\Tenant\Provider\ProviderTeamController;
use App\Http\Controllers\Api\V1\Tenant\Provider\ProviderServiceAreaController;
use App\Http\Controllers\Api\V1\Tenant\Provider\ProviderVehicleController;
use App\Http\Controllers\Api\V1\Tenant\SubscriptionController;
use Illuminate\Support\Facades\Route;


/*
|--------------------------------------------------------------------------
| Tenant Domain API Routes
|--------------------------------------------------------------------------
|
| These routes are for tenant domains ({tenant}.deliverynexus.com)
| Used by: Business Owners/Staff, Provider Owners/Staff
| Automatic tenant context - all queries scoped to current tenant
|
*/

Route::middleware('tenant')->group(function () {

    // Tenant info endpoint
    Route::get('/home', function () {
        $tenant = tenant();

        return response()->json([
            'success' => true,
            'tenant' => [
                'id' => $tenant->id,
                'name' => $tenant->name,
                'domain' => $tenant->primaryDomain()?->domain,
                'type' => $tenant->tenant_type,
                'status' => $tenant->status,
                'settings' => $tenant->settings,
                'created_at' => $tenant->created_at,
            ],
            'version' => '1.0.0',
        ]);
    });
    // Tenant-specific authentication (minimal duplication for UX)
    // Use 'tenant-auth' prefix to avoid conflicts with central auth routes
    Route::prefix('tenant-auth')->group(function () {
        Route::post('/login', [ApiAuthController::class, 'tenantLogin']); // Tenant-specific login
        Route::post('/logout', [ApiAuthController::class, 'logout'])->middleware('auth:sanctum');
        Route::post('/refresh', [ApiAuthController::class, 'refreshToken'])->middleware('auth:sanctum');
        Route::get('/me', [ApiAuthController::class, 'me'])->middleware('auth:sanctum');
        // Note: Registration happens via central domain (users are invited to tenants)
        // Note: Password reset via central domain
    });

    // Protected routes (require authentication and tenant context)
    Route::middleware(['auth:sanctum'])->group(function () {

        // Business Management routes (business tenant users only)
        Route::prefix('business')->middleware(['tenant:business'])->group(function () {

            // Business profile and settings
            Route::get('/profile', [BusinessProfileController::class, 'show']);
            Route::put('/profile', [BusinessProfileController::class, 'update']);
            Route::post('/logo', [BusinessProfileController::class, 'uploadLogo']);
            Route::delete('/logo', [BusinessProfileController::class, 'deleteLogo']);
            Route::get('/statistics', [BusinessProfileController::class, 'statistics']);

            // Business settings (operational settings using existing Phase 1 columns)
            Route::get('/settings', [BusinessSettingsController::class, 'show']);
            Route::put('/settings', [BusinessSettingsController::class, 'update']);
            Route::post('/settings/reset', [BusinessSettingsController::class, 'reset']);

            // Order management
            Route::apiResource('orders', BusinessOrderController::class)->except(['store']);
            Route::post('orders/{order}/accept', [BusinessOrderController::class, 'accept']);
            Route::post('orders/{order}/reject', [BusinessOrderController::class, 'reject']);
            Route::post('orders/{order}/prepare', [BusinessOrderController::class, 'prepare']);
            Route::post('orders/{order}/ready', [BusinessOrderController::class, 'ready']);
            Route::post('orders/{order}/complete', [BusinessOrderController::class, 'complete']);
            Route::post('orders/{order}/cancel', [BusinessOrderController::class, 'cancel']);

            // Product and inventory management
            Route::apiResource('products', BusinessProductController::class);
            Route::post('products/{product}/toggle-availability', [BusinessProductController::class, 'toggleAvailability']);
            Route::post('products/{product}/update-stock', [BusinessProductController::class, 'updateStock']);
            Route::post('products/{product}/upload-image', [BusinessProductController::class, 'uploadImage']);
            Route::post('products/bulk-update', [BusinessProductController::class, 'bulkUpdate']);

            // Category management
            Route::apiResource('categories', BusinessCategoryController::class);

            // Product collection management
            Route::apiResource('product-collections', BusinessProductCollectionController::class);
            Route::post('categories/{category}/reorder', [BusinessCategoryController::class, 'reorder']);

            // Team member management
            Route::apiResource('team-members', BusinessTeamController::class)->names([
                'index' => 'business.team-members.index',
                'show' => 'business.team-members.show',
                'store' => 'business.team-members.store',
                'update' => 'business.team-members.update',
                'destroy' => 'business.team-members.destroy',
            ]);
            Route::post('team-members/{member}/assign-role', [BusinessTeamController::class, 'assignRole']);
            Route::post('team-members/{member}/remove-role', [BusinessTeamController::class, 'removeRole']);
            Route::post('team-members/{member}/activate', [BusinessTeamController::class, 'activate']);
            Route::post('team-members/{member}/deactivate', [BusinessTeamController::class, 'deactivate']);

            // Business admin management (moved from central routes)
            Route::get('/admins', [BusinessTeamController::class, 'getAdmins']);
            Route::post('/assign-admin', [BusinessTeamController::class, 'assignAdmin']);
            Route::post('/transfer-admin', [BusinessTeamController::class, 'transferOwnership']);
            Route::put('/admins/{user}/role', [BusinessTeamController::class, 'updateAdminRole']);
            Route::delete('/admins/{user}', [BusinessTeamController::class, 'removeAdmin']);

            // Pickup slot management
            Route::apiResource('pickup-slots', BusinessPickupSlotController::class)->names([
                'index' => 'business.pickup-slots.index',
                'show' => 'business.pickup-slots.show',
                'store' => 'business.pickup-slots.store',
                'update' => 'business.pickup-slots.update',
                'destroy' => 'business.pickup-slots.destroy',
            ]);
            Route::get('pickup-slots-schedule/weekly', [BusinessPickupSlotController::class, 'weeklySchedule']);
            Route::get('pickup-slots-schedule/available', [BusinessPickupSlotController::class, 'availableForDate']);

            // Delivery management
            Route::apiResource('deliveries', BusinessDeliveryController::class)->except(['update', 'destroy']);
            Route::post('deliveries/{delivery}/cancel', [BusinessDeliveryController::class, 'cancel']);

            // Payout management
            Route::apiResource('payouts', BusinessPayoutController::class)->only(['index', 'store', 'show']);

            // Product catalog customization
            Route::get('branches/{branchId}/product-collections', [BusinessProductCatalogController::class, 'getBranchMenuCollections']);
            Route::get('branches/{branchId}/products', [BusinessProductCatalogController::class, 'getBranchProducts']);
            Route::post('branches/{branchId}/catalog-customizations', [BusinessProductCatalogController::class, 'createBranchCustomization']);
            Route::put('catalog-customizations/{customizationId}', [BusinessProductCatalogController::class, 'updateBranchCustomization']);
            Route::delete('catalog-customizations/{customizationId}', [BusinessProductCatalogController::class, 'deleteBranchCustomization']);
            Route::post('branches/{branchId}/product-overrides', [BusinessProductCatalogController::class, 'createProductOverride']);
            Route::post('branches/{sourceBranchId}/copy-to/{targetBranchId}', [BusinessProductCatalogController::class, 'copyBranchCustomizations']);

            // Delivery assignment (first-to-accept)
            Route::post('orders/{orderId}/first-to-accept', [BusinessDeliveryAssignmentController::class, 'initiateFirstToAccept']);
            Route::get('orders/{orderId}/assignment-status', [BusinessDeliveryAssignmentController::class, 'getAssignmentStatus']);
            Route::get('broadcasts/{broadcastId}/stats', [BusinessDeliveryAssignmentController::class, 'getBroadcastStats']);
            Route::get('first-to-accept/stats', [BusinessDeliveryAssignmentController::class, 'getFirstToAcceptStats']);
            Route::post('orders/{orderId}/cancel-delivery-requests', [BusinessDeliveryAssignmentController::class, 'cancelDeliveryRequests']);

            // WhatsApp Business API configuration
            Route::prefix('whatsapp')->group(function () {
                Route::get('/config', [BusinessWhatsAppConfigController::class, 'show']);
                Route::post('/config', [BusinessWhatsAppConfigController::class, 'store']);
                Route::put('/config/settings', [BusinessWhatsAppConfigController::class, 'updateSettings']);
                Route::post('/config/test', [BusinessWhatsAppConfigController::class, 'test']);
                Route::delete('/config', [BusinessWhatsAppConfigController::class, 'destroy']);
            });

            // QR Code generation and management
            Route::prefix('qr-codes')->group(function () {
                Route::post('/menu', [BusinessQrCodeController::class, 'generateMenuQrCode']);
                Route::post('/categories/{categoryId}', [BusinessQrCodeController::class, 'generateCategoryQrCode']);
                Route::get('/history', [BusinessQrCodeController::class, 'getQrCodeHistory']);
                Route::get('/analytics', [BusinessQrCodeController::class, 'getQrCodeAnalytics']);
                Route::get('/{qrCodeId}/download', [BusinessQrCodeController::class, 'downloadQrCode']);
                Route::post('/bulk-generate', [BusinessQrCodeController::class, 'bulkGenerateQrCodes']);
            });

            // Analytics and reporting
            Route::get('/dashboard', [BusinessDashboardController::class, 'index']);
            Route::get('/analytics/revenue', [BusinessAnalyticsController::class, 'revenue']);
            Route::get('/analytics/products', [BusinessAnalyticsController::class, 'products']);
            Route::get('/analytics/customers', [BusinessAnalyticsController::class, 'customers']);
        });

        // Provider Management routes (provider tenant users only)
        Route::prefix('provider')->middleware(['tenant:provider'])->group(function () {

            // Provider profile and settings
            Route::get('/profile', [ProviderProfileController::class, 'show']);
            Route::put('/profile', [ProviderProfileController::class, 'update']);
            Route::post('/logo', [ProviderProfileController::class, 'uploadLogo']);
            Route::delete('/logo', [ProviderProfileController::class, 'deleteLogo']);

            // Provider settings (operational settings using existing Phase 1 columns)
            Route::get('/settings', [ProviderSettingsController::class, 'show']);
            Route::put('/settings', [ProviderSettingsController::class, 'update']);
            Route::put('/settings/working-hours', [ProviderSettingsController::class, 'updateWorkingHours']);
            Route::put('/settings/service-areas', [ProviderSettingsController::class, 'updateServiceAreas']);
            Route::post('/settings/reset', [ProviderSettingsController::class, 'reset']);

            // Delivery management
            Route::apiResource('deliveries', ProviderDeliveryController::class)->except(['store'])->names([
                'index' => 'provider.deliveries.index',
                'show' => 'provider.deliveries.show',
                'update' => 'provider.deliveries.update',
                'destroy' => 'provider.deliveries.destroy',
            ]);
            Route::post('deliveries/{delivery}/accept', [ProviderDeliveryController::class, 'accept']);
            Route::post('deliveries/{delivery}/reject', [ProviderDeliveryController::class, 'reject']);
            Route::post('deliveries/{delivery}/assign-driver', [ProviderDeliveryController::class, 'assignDriver']);
            Route::post('deliveries/{delivery}/start', [ProviderDeliveryController::class, 'start']);
            Route::post('deliveries/{delivery}/pickup', [ProviderDeliveryController::class, 'pickup']);
            Route::post('deliveries/{delivery}/complete', [ProviderDeliveryController::class, 'complete']);
            Route::post('deliveries/{delivery}/cancel', [ProviderDeliveryController::class, 'cancel']);
            Route::post('deliveries/{delivery}/update-location', [ProviderDeliveryController::class, 'updateLocation']);

            // Driver management
            Route::apiResource('drivers', ProviderDriverController::class)->names([
                'index' => 'provider.drivers.index',
                'show' => 'provider.drivers.show',
                'store' => 'provider.drivers.store',
                'update' => 'provider.drivers.update',
                'destroy' => 'provider.drivers.destroy',
            ]);
            Route::post('drivers/{driver}/activate', [ProviderDriverController::class, 'activate']);
            Route::post('drivers/{driver}/deactivate', [ProviderDriverController::class, 'deactivate']);
            Route::post('drivers/{driver}/verify', [ProviderDriverController::class, 'verify']);
            Route::get('drivers/{driver}/location', [ProviderDriverController::class, 'getLocation']);
            Route::post('drivers/{driver}/location', [ProviderDriverController::class, 'updateLocation']);

            // Driver performance tracking
            Route::get('drivers/{driver}/performance', [ProviderDriverPerformanceController::class, 'show']);
            Route::post('drivers/{driver}/performance/rate', [ProviderDriverPerformanceController::class, 'rate']);
            Route::get('drivers/leaderboard', [ProviderDriverPerformanceController::class, 'leaderboard']);

            // Vehicle management
            Route::apiResource('vehicles', ProviderVehicleController::class)->only(['index', 'show', 'store'])->names([
                'index' => 'provider.vehicles.index',
                'show' => 'provider.vehicles.show',
                'store' => 'provider.vehicles.store',
            ]);
            Route::post('vehicles/{vehicle}/assign-driver', [ProviderVehicleController::class, 'assignDriver']);
            Route::post('vehicles/{vehicle}/unassign-driver', [ProviderVehicleController::class, 'unassignDriver']);
            Route::get('vehicles/utilization', [ProviderVehicleController::class, 'utilization']);

            // Service area management
            Route::apiResource('service-areas', ProviderServiceAreaController::class)->names([
                'index' => 'provider.service-areas.index',
                'show' => 'provider.service-areas.show',
                'store' => 'provider.service-areas.store',
                'update' => 'provider.service-areas.update',
                'destroy' => 'provider.service-areas.destroy',
            ]);
            Route::post('service-areas/{area}/activate', [ProviderServiceAreaController::class, 'activate']);
            Route::post('service-areas/{area}/deactivate', [ProviderServiceAreaController::class, 'deactivate']);

            // Team member management
            Route::apiResource('team-members', ProviderTeamController::class)->names([
                'index' => 'provider.team-members.index',
                'show' => 'provider.team-members.show',
                'store' => 'provider.team-members.store',
                'update' => 'provider.team-members.update',
                'destroy' => 'provider.team-members.destroy',
            ]);
            Route::post('team-members/{member}/assign-role', [ProviderTeamController::class, 'assignRole']);
            Route::post('team-members/{member}/remove-role', [ProviderTeamController::class, 'removeRole']);
            Route::post('team-members/{member}/activate', [ProviderTeamController::class, 'activate']);
            Route::post('team-members/{member}/deactivate', [ProviderTeamController::class, 'deactivate']);

            // Payout management
            Route::apiResource('payouts', ProviderPayoutController::class)->only(['index', 'store', 'show'])->names([
                'index' => 'provider.payouts.index',
                'show' => 'provider.payouts.show',
                'store' => 'provider.payouts.store',
            ]);

            // Delivery requests (first-to-accept)
            Route::get('delivery-requests', [ProviderDeliveryRequestController::class, 'index']);
            Route::get('delivery-requests/pending', [ProviderDeliveryRequestController::class, 'pending']);
            Route::get('delivery-requests/statistics', [ProviderDeliveryRequestController::class, 'statistics']);
            Route::get('delivery-requests/{requestId}', [ProviderDeliveryRequestController::class, 'show']);
            Route::post('delivery-requests/{requestId}/accept', [ProviderDeliveryRequestController::class, 'accept']);

            // Analytics and reporting
            Route::get('/dashboard', [ProviderDashboardController::class, 'index']);
            Route::get('/analytics', [ProviderAnalyticsController::class, 'index']);
            Route::get('/analytics/deliveries', [ProviderAnalyticsController::class, 'deliveries']);
            Route::get('/analytics/drivers', [ProviderAnalyticsController::class, 'drivers']);
            Route::get('/analytics/revenue', [ProviderAnalyticsController::class, 'revenue']);
            Route::get('/analytics/performance', [ProviderAnalyticsController::class, 'performance']);
        });

        // Driver routes (delivery drivers and riders)
        Route::middleware(['role:delivery-driver|delivery-rider'])->prefix('driver')->group(function () {
            // Real-time tracking
            Route::prefix('tracking')->group(function () {
                Route::post('/location', [ProviderRealTimeTrackingController::class, 'updateLocation']);
                Route::post('/start', [ProviderRealTimeTrackingController::class, 'startTracking']);
                Route::post('/stop', [ProviderRealTimeTrackingController::class, 'stopTracking']);
                Route::get('/deliveries/{delivery_id}/location', [ProviderRealTimeTrackingController::class, 'getCurrentLocation']);
                Route::get('/active-deliveries', [ProviderRealTimeTrackingController::class, 'getActiveDeliveries']);
            });
        });

        // Shared tenant routes (any authenticated tenant user)
        Route::middleware(['tenant:any'])->group(function () {
            // Subscription management (shared for both business and provider tenants)
            Route::prefix('subscription')->group(function () {
                Route::get('/plans', [SubscriptionController::class, 'plans']);
                Route::get('/current', [SubscriptionController::class, 'current']);
                Route::post('/subscribe', [SubscriptionController::class, 'subscribe']);
                Route::post('/upgrade', [SubscriptionController::class, 'upgrade']);
                Route::post('/cancel', [SubscriptionController::class, 'cancel']);
                Route::get('/features/{feature_slug}', [SubscriptionController::class, 'checkFeature']);
                Route::get('/usage', [SubscriptionController::class, 'usage']);
            });

            // File Management Routes
            Route::prefix('files')->group(function () {
                Route::post('upload', [App\Http\Controllers\Api\V1\Tenant\FileController::class, 'upload']);
                Route::get('download/{path}', [App\Http\Controllers\Api\V1\Tenant\FileController::class, 'download']);
                Route::delete('{path}', [App\Http\Controllers\Api\V1\Tenant\FileController::class, 'delete']);
                Route::get('category/{category}', [App\Http\Controllers\Api\V1\Tenant\FileController::class, 'listByCategory']);
            });

            // User Profile Management routes (tenant controller for tenant users only)
            // Using /tenant prefix to avoid conflicts with central routes
            Route::prefix('tenant/user')->group(function () {
                Route::get('/profile', [TenantUserProfileController::class, 'show']);
                Route::put('/profile', [TenantUserProfileController::class, 'update']);
                Route::post('/change-password', [TenantUserProfileController::class, 'changePassword']);
                Route::get('/preferences', [TenantUserProfileController::class, 'getPreferences']);
                Route::put('/preferences', [TenantUserProfileController::class, 'updatePreferences']);
                Route::post('/deactivate', [TenantUserProfileController::class, 'deactivate']);
            });

            // Notifications (standardized to match central)
            Route::prefix('notifications')->group(function () {
                Route::get('/', [NotificationController::class, 'index']);
                Route::get('/stats', [NotificationController::class, 'stats']);
                Route::get('/{id}', [NotificationController::class, 'show']);
                Route::put('/{id}/read', [NotificationController::class, 'markAsRead']);
                Route::put('/mark-all-read', [NotificationController::class, 'markAllAsRead']);
                Route::delete('/{id}', [NotificationController::class, 'destroy']);

                // FCM token management
                Route::post('/fcm-tokens', [NotificationController::class, 'registerFcmToken']);
                Route::get('/fcm-tokens', [NotificationController::class, 'getFcmTokens']);
                Route::delete('/fcm-tokens', [NotificationController::class, 'removeFcmToken']);

                // Notification preferences
                Route::get('/preferences', [NotificationController::class, 'getPreferences']);
                Route::put('/preferences', [NotificationController::class, 'updatePreferences']);
                Route::post('/preferences/reset', [NotificationController::class, 'resetPreferences']);
            });

        });
    });
});
