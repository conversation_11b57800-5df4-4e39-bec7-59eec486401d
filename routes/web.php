<?php

declare(strict_types=1);

use App\Http\Controllers\Api\V1\Auth\WebAuthController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes (Session-based Authentication)
|--------------------------------------------------------------------------
|
| Simple session-based authentication routes for web dashboard.
| CSRF protected with httpOnly cookies for NextJS frontend.
|
*/

// Root route for web application
Route::get('/', function () {
    return response()->json([
        'success' => true,
        'message' => 'DeliveryNexus Web API',
        'version' => '1.0.0',
        'documentation' => url('/docs'),
    ]);
});

// Web authentication routes (CSRF protected, session-based versions of API routes)
Route::prefix('auth/web')->middleware(['throttle:5,1'])->group(function () {
    // Unauthenticated routes (session-based versions)
    Route::post('/login', [WebAuthController::class, 'login']);
    Route::post('/password/reset', [WebAuthController::class, 'sendPasswordReset']);
    Route::post('/password/confirm', [WebAuthController::class, 'resetPassword']);

    // Progressive registration flow (session-based versions)
    Route::post('/register', [WebAuthController::class, 'register']);
    Route::post('/select-account-type', [WebAuthController::class, 'selectAccountType']);
    Route::post('/onboard/business', [WebAuthController::class, 'onboardBusiness']);
    Route::post('/onboard/provider', [WebAuthController::class, 'onboardProvider']);

    // Authenticated routes
    Route::middleware('auth:sanctum')->group(function () {
        Route::post('/logout', [WebAuthController::class, 'logout']);
        Route::post('/logout-all', [WebAuthController::class, 'logoutFromAllDevices']);
        Route::post('/refresh', [WebAuthController::class, 'refresh']);
        Route::get('/me', [WebAuthController::class, 'me']);

        // Email/phone verification (commonly needed for web dashboard)
        Route::post('/email/send-verification', [WebAuthController::class, 'sendEmailVerification']);
        Route::post('/email/verify', [WebAuthController::class, 'verifyEmail']);
        Route::post('/phone/send-verification', [WebAuthController::class, 'sendPhoneVerification']);
        Route::post('/phone/verify', [WebAuthController::class, 'verifyPhone']);
    });
});
