<?php

declare(strict_types=1);

use App\Http\Controllers\Api\V1\Tenant\Business\BusinessProductBundleController;
use App\Models\Business\Business;
use App\Models\Business\Product;
use App\Models\Business\ProductBundle;
use App\Models\Business\ProductCategory;
use App\Models\System\Tenant;
use App\Models\User\User;
use App\Services\Business\BusinessService;
use App\Services\System\LoggingService;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

describe('ProductBundleController', function () {
    beforeEach(function () {
        // Create tenant and set context
        $this->tenant = Tenant::factory()->create();
        tenancy()->initialize($this->tenant);

        // Create user and business
        $this->user = User::factory()->create(['tenant_id' => $this->tenant->id]);
        $this->business = Business::factory()->create([
            'tenant_id' => $this->tenant->id,
            'user_id' => $this->user->id,
        ]);

        // Create category and products
        $this->category = ProductCategory::create([
            'tenant_id' => $this->tenant->id,
            'business_id' => $this->business->id,
            'name' => 'Test Category',
            'slug' => 'test-category',
            'is_active' => true,
            'display_order' => 1,
        ]);

        $this->product1 = Product::factory()->create([
            'business_id' => $this->business->id,
            'category_id' => $this->category->id,
            'name' => 'Burger',
            'price' => 1500,
        ]);

        $this->product2 = Product::factory()->create([
            'business_id' => $this->business->id,
            'category_id' => $this->category->id,
            'name' => 'Fries',
            'price' => 800,
        ]);

        // Create bundle
        $this->bundle = ProductBundle::create([
            'tenant_id' => $this->tenant->id,
            'business_id' => $this->business->id,
            'name' => 'Combo Meal',
            'price' => 2000,
            'discount_amount' => 200,
            'is_active' => true,
        ]);

        // Mock services
        $this->businessService = Mockery::mock(BusinessService::class);
        $this->loggingService = Mockery::mock(LoggingService::class);

        $this->businessService->shouldReceive('getCurrentBusiness')
            ->andReturn($this->business);

        $this->loggingService->shouldReceive('logInfo')->andReturn(true);
        $this->loggingService->shouldReceive('logError')->andReturn(true);

        // Create controller
        $this->controller = new BusinessProductBundleController(
            $this->businessService,
            $this->loggingService
        );

        // Set authenticated user
        $this->actingAs($this->user);
    });

    describe('index method', function () {
        it('can retrieve product bundles list with default parameters', function () {
            $request = new \Illuminate\Http\Request;
            $response = $this->controller->index($request);

            expect($response->getStatusCode())->toBe(200);

            $data = json_decode($response->getContent(), true);
            expect($data['success'])->toBeTrue();
            expect($data['message'])->toBe('Product bundles retrieved successfully');
            expect($data['data'])->toHaveKey('data');
            expect($data['data']['data'])->toBeArray();
        });

        it('limits pagination parameters to maximum', function () {
            $request = new \Illuminate\Http\Request(['per_page' => 150]);
            $response = $this->controller->index($request);

            expect($response->getStatusCode())->toBe(200);

            $data = json_decode($response->getContent(), true);
            expect($data['data']['per_page'])->toBeLessThanOrEqual(100);
        });

        it('returns error when no business found', function () {
            // Create a new controller with a mock that throws exception
            $businessServiceMock = Mockery::mock(BusinessService::class);
            $businessServiceMock->shouldReceive('getCurrentBusiness')
                ->andThrow(new \App\Exceptions\BusinessLogicException('No business found for current tenant'));

            $controller = new BusinessProductBundleController(
                $businessServiceMock,
                $this->loggingService
            );

            $request = new \Illuminate\Http\Request;
            $response = $controller->index($request);

            expect($response->getStatusCode())->toBe(422);

            $data = json_decode($response->getContent(), true);
            expect($data['success'])->toBeFalse();
            expect($data['message'])->toBe('No business found for current tenant');
        });
    });

    describe('show method', function () {
        it('can retrieve specific product bundle', function () {
            $response = $this->controller->show($this->bundle->id);

            expect($response->getStatusCode())->toBe(200);

            $data = json_decode($response->getContent(), true);
            expect($data['success'])->toBeTrue();
            expect($data['message'])->toBe('Product bundle retrieved successfully');
            expect($data['data']['id'])->toBe($this->bundle->id);
            expect($data['data']['name'])->toBe('Combo Meal');
        });

        it('returns 404 for non-existent bundle', function () {
            $nonExistentId = '019723aa-3202-70dd-a0c1-3565681dd87a';

            $response = $this->controller->show($nonExistentId);

            expect($response->getStatusCode())->toBe(404);

            $data = json_decode($response->getContent(), true);
            expect($data['success'])->toBeFalse();
            expect($data['message'])->toBe('Product bundle not found');
        });
    });

    describe('store method', function () {
        it('validates required fields', function () {
            $request = new \Illuminate\Http\Request([]);

            expect(fn () => $this->controller->store($request))
                ->toThrow(\Illuminate\Validation\ValidationException::class);
        });

        it('can create a bundle with valid data', function () {
            $bundleData = [
                'name' => 'New Combo',
                'description' => 'Delicious combo meal',
                'price' => 2500,
                'discount_amount' => 300,
                'is_active' => true,
                'products' => [
                    [
                        'product_id' => $this->product1->id,
                        'quantity' => 1,
                    ],
                    [
                        'product_id' => $this->product2->id,
                        'quantity' => 1,
                    ],
                ],
            ];

            $request = new \Illuminate\Http\Request($bundleData);
            $response = $this->controller->store($request);

            expect($response->getStatusCode())->toBe(201);

            $data = json_decode($response->getContent(), true);
            expect($data['success'])->toBeTrue();
            expect($data['message'])->toBe('Product bundle created successfully');
            expect($data['data']['name'])->toBe('New Combo');
            expect($data['data']['price'])->toBe('2500.00');
        });

        it('validates product existence', function () {
            $bundleData = [
                'name' => 'Invalid Combo',
                'price' => 2500,
                'products' => [
                    [
                        'product_id' => '019723aa-3202-70dd-a0c1-3565681dd87a', // Non-existent
                        'quantity' => 1,
                    ],
                ],
            ];

            $request = new \Illuminate\Http\Request($bundleData);

            expect(fn () => $this->controller->store($request))
                ->toThrow(\Illuminate\Validation\ValidationException::class);
        });
    });

    describe('transformBundle method', function () {
        it('transforms bundle correctly for basic response', function () {
            $transformed = $this->controller->transformBundle($this->bundle);

            expect($transformed)->toHaveKey('id');
            expect($transformed)->toHaveKey('name');
            expect($transformed)->toHaveKey('price');
            expect($transformed)->toHaveKey('is_active');
            expect($transformed)->toHaveKey('products_count');
            expect($transformed['id'])->toBe($this->bundle->id);
            expect($transformed['name'])->toBe('Combo Meal');
        });

        it('includes products in detailed response', function () {
            // Load bundle products relationship
            $this->bundle->load('bundleProducts.product');

            $transformed = $this->controller->transformBundle($this->bundle, true);

            expect($transformed)->toHaveKey('products');
            expect($transformed['products'])->toBeArray();
        });
    });

    describe('error handling', function () {
        it('returns error when no business found', function () {
            // Create a new controller with a mock that throws exception
            $businessServiceMock = Mockery::mock(BusinessService::class);
            $businessServiceMock->shouldReceive('getCurrentBusiness')
                ->andThrow(new \App\Exceptions\BusinessLogicException('No business found for current tenant'));

            $controller = new BusinessProductBundleController(
                $businessServiceMock,
                $this->loggingService
            );

            $request = new \Illuminate\Http\Request;
            $response = $controller->index($request);

            expect($response->getStatusCode())->toBe(422);

            $data = json_decode($response->getContent(), true);
            expect($data['success'])->toBeFalse();
            expect($data['message'])->toBe('No business found for current tenant');
        });
    });
});
