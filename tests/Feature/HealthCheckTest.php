<?php

declare(strict_types=1);

use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

describe('Health Check API', function () {
    it('returns basic health status', function () {
        $response = $this->get('/api/v1/health');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'timestamp',
                'data' => [
                    'status',
                    'service',
                    'version',
                ],
            ])
            ->assertJson([
                'success' => true,
                'message' => 'System is healthy',
                'data' => [
                    'status' => 'ok',
                    'service' => 'DeliveryNexus API',
                    'version' => '1.0.0',
                ],
            ]);
    });

    it('returns detailed health status', function () {
        $response = $this->get('/api/v1/health/detailed');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'timestamp',
                'data' => [
                    'status',
                    'service',
                    'version',
                    'checks' => [
                        'database',
                        'cache',
                        'queue',
                    ],
                    'system' => [
                        'php_version',
                        'laravel_version',
                        'memory_usage',
                        'disk_usage',
                    ],
                ],
            ]);
    });

    it('includes database connection status in detailed check', function () {
        $response = $this->get('/api/v1/health/detailed');

        $response->assertStatus(200)
            ->assertJsonPath('data.checks.database.status', 'ok')
            ->assertJsonPath('data.checks.database.connection', config('database.default'));
    });

    it('includes cache status in detailed check', function () {
        $response = $this->get('/api/v1/health/detailed');

        $response->assertStatus(200)
            ->assertJsonPath('data.checks.cache.status', 'ok');
    });

    it('includes queue status in detailed check', function () {
        $response = $this->get('/api/v1/health/detailed');

        $response->assertStatus(200)
            ->assertJsonPath('data.checks.queue.status', 'ok');
    });

    it('includes system information in detailed check', function () {
        $response = $this->get('/api/v1/health/detailed');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'system' => [
                        'php_version',
                        'laravel_version',
                        'memory_usage',
                        'disk_usage',
                    ],
                ],
            ]);

        $data = $response->json();
        expect($data['data']['system']['php_version'])->toStartWith('8.');
        expect($data['data']['system']['laravel_version'])->toStartWith('12.');
    });
});
