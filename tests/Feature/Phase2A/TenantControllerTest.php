<?php

declare(strict_types=1);

use App\Enums\System\TenantStatus;
use App\Models\System\Tenant;
use App\Models\User\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Create a platform admin user for testing
    $this->admin = User::factory()->create();

    // Assign platform-admin role using <PERSON><PERSON><PERSON>
    \Silber\Bouncer\BouncerFacade::assign('platform-admin')->to($this->admin);

    // Authenticate as the admin user
    $this->actingAs($this->admin);
});

test('can list tenants', function () {
    // Create some test tenants
    Tenant::factory()->count(3)->create();

    $response = $this->getJson('/api/v1/admin/tenants');

    $response->assertStatus(200)
        ->assertJsonStructure([
            'success',
            'message',
            'data' => [
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'tenant_type',
                        'status',
                        'created_at',
                    ],
                ],
                'current_page',
                'total',
            ],
        ]);
});

test('can create a business tenant', function () {
    $tenantData = [
        'name' => 'Test Business',
        'tenant_type' => \App\Enums\Financial\SubscriptionTargetType::BUSINESS->value,
        'subdomain' => 'testbusiness',
    ];

    $response = $this->postJson('/api/v1/admin/tenants', $tenantData);

    $response->assertStatus(201)
        ->assertJsonStructure([
            'success',
            'message',
            'data' => [
                'id',
                'name',
                'tenant_type',
                'status',
                'domains',
            ],
        ]);

    $this->assertDatabaseHas('tenants', [
        'name' => 'Test Business',
        'tenant_type' => \App\Enums\Financial\SubscriptionTargetType::BUSINESS->value,
    ]);
});

test('can get tenant details', function () {
    $tenant = Tenant::factory()->create();

    $response = $this->getJson("/api/v1/admin/tenants/{$tenant->id}");

    $response->assertStatus(200)
        ->assertJsonStructure([
            'success',
            'message',
            'data' => [
                'id',
                'name',
                'tenant_type',
                'status',
                'created_at',
            ],
        ]);
});

test('can update tenant', function () {
    $tenant = Tenant::factory()->create(['name' => 'Original Name']);

    $updateData = [
        'name' => 'Updated Name',
    ];

    $response = $this->putJson("/api/v1/admin/tenants/{$tenant->id}", $updateData);

    $response->assertStatus(200);

    $this->assertDatabaseHas('tenants', [
        'id' => $tenant->id,
        'name' => 'Updated Name',
    ]);
});

test('can archive tenant', function () {
    $tenant = Tenant::factory()->create(['status' => TenantStatus::ACTIVE]);

    $response = $this->deleteJson("/api/v1/admin/tenants/{$tenant->id}", [
        'reason' => 'Test archival',
    ]);

    $response->assertStatus(200);

    $this->assertDatabaseHas('tenants', [
        'id' => $tenant->id,
        'status' => TenantStatus::ARCHIVED->value,
    ]);
});

test('can restore archived tenant', function () {
    $tenant = Tenant::factory()->create([
        'status' => TenantStatus::ARCHIVED,
        'archived_at' => now(),
    ]);

    $response = $this->postJson("/api/v1/admin/tenants/{$tenant->id}/restore");

    $response->assertStatus(200);

    $this->assertDatabaseHas('tenants', [
        'id' => $tenant->id,
        'status' => TenantStatus::ACTIVE->value,
    ]);
});

test('can update tenant status', function () {
    $tenant = Tenant::factory()->create(['status' => TenantStatus::ACTIVE]);

    $response = $this->putJson("/api/v1/admin/tenants/{$tenant->id}/status", [
        'status' => TenantStatus::SUSPENDED->value,
        'reason' => 'Test suspension',
    ]);

    $response->assertStatus(200);

    $this->assertDatabaseHas('tenants', [
        'id' => $tenant->id,
        'status' => TenantStatus::SUSPENDED->value,
    ]);
});

test('can get tenant statistics', function () {
    // Create test data
    Tenant::factory()->count(2)->create(['status' => TenantStatus::ACTIVE]);
    Tenant::factory()->count(1)->create(['status' => TenantStatus::SUSPENDED]);

    $response = $this->getJson('/api/v1/admin/tenants/statistics');

    $response->assertStatus(200)
        ->assertJsonStructure([
            'success',
            'message',
            'data' => [
                'total_tenants',
                'active_tenants',
                'suspended_tenants',
                'archived_tenants',
                'business_tenants',
                'provider_tenants',
            ],
        ]);
});

test('can check subdomain availability', function () {
    $response = $this->getJson('/api/v1/admin/tenants/check-subdomain?subdomain=available');

    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'data' => [
                'available' => true,
            ],
        ]);
});

test('validates tenant creation data', function () {
    $invalidData = [
        'name' => '', // Required field missing
        'tenant_type' => 'invalid_type', // Invalid enum value
    ];

    $response = $this->postJson('/api/v1/admin/tenants', $invalidData);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['name', 'tenant_type']);
});

test('returns 404 for non-existent tenant', function () {
    $response = $this->getJson('/api/v1/admin/tenants/non-existent-id');

    $response->assertStatus(404);
});
