<?php

declare(strict_types=1);

use App\Enums\Financial\SubscriptionTargetType;
use App\Enums\System\TenantStatus;
use App\Exceptions\BusinessLogicException;
use App\Models\System\Domain;
use App\Models\System\Tenant;
use App\Models\User\User;
use App\Services\System\LoggingService;
use App\Services\System\TenantService;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->loggingService = Mockery::mock(LoggingService::class);
    $this->loggingService->shouldReceive('logAudit')->andReturn(true);
    $this->loggingService->shouldReceive('logError')->andReturn(true);

    $this->tenantService = new TenantService($this->loggingService);
});

test('can create a business tenant', function () {
    $user = User::factory()->create();

    $tenantData = [
        'name' => 'Test Business',
        'tenant_type' => \App\Enums\Financial\SubscriptionTargetType::BUSINESS->value,
        'subdomain' => 'testbusiness',
        'created_by' => $user->id,
    ];

    $tenant = $this->tenantService->createTenant($tenantData);

    expect($tenant)->toBeInstanceOf(Tenant::class);
    expect($tenant->name)->toBe('Test Business');
    expect($tenant->tenant_type)->toBe(\App\Enums\Financial\SubscriptionTargetType::BUSINESS);
    expect($tenant->status)->toBe(TenantStatus::ACTIVE);
    expect($tenant->created_by)->toBe($user->id);

    // Check domain was created
    expect($tenant->domains)->toHaveCount(1);
    $baseDomain = config('tenancy.central_domains')[0] ?? 'deliverynexus.test';
    expect($tenant->domains->first()->domain)->toBe('testbusiness.'.$baseDomain);
});

test('can create a delivery provider tenant', function () {
    $user = User::factory()->create();

    $tenantData = [
        'name' => 'Test Provider',
        'tenant_type' => SubscriptionTargetType::PROVIDER->value,
        'created_by' => $user->id,
    ];

    $tenant = $this->tenantService->createTenant($tenantData);

    expect($tenant)->toBeInstanceOf(Tenant::class);
    expect($tenant->name)->toBe('Test Provider');
    expect($tenant->tenant_type)->toBe(SubscriptionTargetType::PROVIDER);
    expect($tenant->status)->toBe(TenantStatus::ACTIVE);
});

test('throws exception for invalid tenant type', function () {
    $tenantData = [
        'name' => 'Test Tenant',
        'tenant_type' => 'invalid_type',
    ];

    expect(fn () => $this->tenantService->createTenant($tenantData))
        ->toThrow(BusinessLogicException::class, 'Invalid tenant type');
});

test('can get tenant by id', function () {
    $tenant = Tenant::factory()->create();

    $result = $this->tenantService->getTenant($tenant->id);

    expect($result->id)->toBe($tenant->id);
});

test('throws exception when tenant not found', function () {
    expect(fn () => $this->tenantService->getTenant('non-existent-id'))
        ->toThrow(BusinessLogicException::class, 'Tenant not found');
});

test('can update tenant', function () {
    $tenant = Tenant::factory()->create(['name' => 'Original Name']);

    $updatedTenant = $this->tenantService->updateTenant($tenant->id, [
        'name' => 'Updated Name',
    ]);

    expect($updatedTenant->name)->toBe('Updated Name');
});

test('can archive tenant', function () {
    $tenant = Tenant::factory()->create(['status' => TenantStatus::ACTIVE]);

    $archivedTenant = $this->tenantService->archiveTenant($tenant->id, 'Test reason');

    expect($archivedTenant->status)->toBe(TenantStatus::ARCHIVED);
    expect($archivedTenant->archived_at)->not->toBeNull();
});

test('cannot archive already archived tenant', function () {
    $tenant = Tenant::factory()->create(['status' => TenantStatus::ARCHIVED]);

    expect(fn () => $this->tenantService->archiveTenant($tenant->id))
        ->toThrow(BusinessLogicException::class, 'Tenant is already archived');
});

test('can restore archived tenant', function () {
    $tenant = Tenant::factory()->create([
        'status' => TenantStatus::ARCHIVED,
        'archived_at' => now(),
    ]);

    $restoredTenant = $this->tenantService->restoreTenant($tenant->id);

    expect($restoredTenant->status)->toBe(TenantStatus::ACTIVE);
    expect($restoredTenant->archived_at)->toBeNull();
});

test('cannot restore non-archived tenant', function () {
    $tenant = Tenant::factory()->create(['status' => TenantStatus::ACTIVE]);

    expect(fn () => $this->tenantService->restoreTenant($tenant->id))
        ->toThrow(BusinessLogicException::class, 'Only archived tenants can be restored');
});

test('can update tenant status', function () {
    $tenant = Tenant::factory()->create(['status' => TenantStatus::ACTIVE]);

    $updatedTenant = $this->tenantService->updateTenantStatus(
        $tenant->id,
        TenantStatus::SUSPENDED,
        'Test suspension'
    );

    expect($updatedTenant->status)->toBe(TenantStatus::SUSPENDED);
});

test('validates status transitions', function () {
    $tenant = Tenant::factory()->create(['status' => TenantStatus::ARCHIVED]);

    expect(fn () => $this->tenantService->updateTenantStatus($tenant->id, TenantStatus::SUSPENDED))
        ->toThrow(BusinessLogicException::class, 'Invalid status transition');
});

test('can get tenant statistics', function () {
    Tenant::factory()->count(3)->create(['status' => TenantStatus::ACTIVE]);
    Tenant::factory()->count(2)->create(['status' => TenantStatus::SUSPENDED]);
    Tenant::factory()->count(1)->create(['status' => TenantStatus::ARCHIVED]);

    $stats = $this->tenantService->getTenantStatistics();

    expect($stats['total_tenants'])->toBe(6);
    expect($stats['active_tenants'])->toBe(3);
    expect($stats['suspended_tenants'])->toBe(2);
    expect($stats['archived_tenants'])->toBe(1);
});

test('can check subdomain availability', function () {
    // Available subdomain
    expect($this->tenantService->isSubdomainAvailable('available'))->toBeTrue();

    // Create a domain to make subdomain unavailable
    $baseDomain = config('tenancy.central_domains')[0] ?? 'deliverynexus.test';
    Domain::factory()->create(['domain' => 'taken.'.$baseDomain]);
    expect($this->tenantService->isSubdomainAvailable('taken'))->toBeFalse();

    // Invalid format
    expect($this->tenantService->isSubdomainAvailable('INVALID'))->toBeFalse();
});

test('prevents duplicate subdomains', function () {
    $user = User::factory()->create();

    // Create first tenant with subdomain
    $this->tenantService->createTenant([
        'name' => 'First Tenant',
        'tenant_type' => \App\Enums\Financial\SubscriptionTargetType::BUSINESS->value,
        'subdomain' => 'duplicate',
        'created_by' => $user->id,
    ]);

    // Try to create second tenant with same subdomain
    expect(fn () => $this->tenantService->createTenant([
        'name' => 'Second Tenant',
        'tenant_type' => \App\Enums\Financial\SubscriptionTargetType::BUSINESS->value,
        'subdomain' => 'duplicate',
        'created_by' => $user->id,
    ]))->toThrow(BusinessLogicException::class, 'Subdomain is already taken');
});
