<?php

declare(strict_types=1);

use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

describe('Webhook API', function () {
    beforeEach(function () {
        config([
            'services.paystack.webhook_secret' => 'webhook_secret_123',
        ]);
    });

    describe('Paystack Webhook', function () {
        it('can process valid paystack webhook', function () {
            $payload = json_encode([
                'event' => 'charge.success',
                'data' => [
                    'reference' => 'test_ref_123',
                    'amount' => 100000,
                    'status' => 'success',
                    'customer' => [
                        'email' => '<EMAIL>',
                    ],
                ],
            ]);

            $signature = hash_hmac('sha512', $payload, 'webhook_secret_123');

            $response = $this->withHeaders([
                'x-paystack-signature' => $signature,
                'Content-Type' => 'application/json',
            ])->postJson('/api/v1/webhooks/paystack', json_decode($payload, true));

            $response->assertStatus(200)
                ->assertJson(['status' => 'success']);
        });

        it('rejects webhook without signature', function () {
            $payload = [
                'event' => 'charge.success',
                'data' => [
                    'reference' => 'test_ref_123',
                ],
            ];

            $response = $this->postJson('/api/v1/webhooks/paystack', $payload);

            $response->assertStatus(400)
                ->assertJson(['error' => 'Missing signature']);
        });

        it('rejects webhook with invalid signature', function () {
            $payload = json_encode([
                'event' => 'charge.success',
                'data' => [
                    'reference' => 'test_ref_123',
                ],
            ]);

            $response = $this->withHeaders([
                'x-paystack-signature' => 'invalid_signature',
                'Content-Type' => 'application/json',
            ])->postJson('/api/v1/webhooks/paystack', json_decode($payload, true));

            $response->assertStatus(401)
                ->assertJson(['error' => 'Invalid signature']);
        });

        it('handles malformed JSON payload', function () {
            // Create a valid signature for the malformed JSON payload
            $malformedPayload = 'invalid json';
            $signature = hash_hmac('sha512', $malformedPayload, 'webhook_secret_123');

            // Send raw content with proper headers
            $response = $this->call(
                'POST',
                '/api/v1/webhooks/paystack',
                [], // parameters
                [], // cookies
                [], // files
                [
                    'HTTP_X_PAYSTACK_SIGNATURE' => $signature,
                    'CONTENT_TYPE' => 'application/json',
                ], // server
                $malformedPayload // content
            );

            // Should return 400 with Invalid JSON error after signature validation passes
            $response->assertStatus(400)
                ->assertJson(['error' => 'Invalid JSON']);
        });

        it('processes charge.failed webhook', function () {
            $payload = json_encode([
                'event' => 'charge.failed',
                'data' => [
                    'reference' => 'test_ref_123',
                    'gateway_response' => 'Insufficient funds',
                ],
            ]);

            $signature = hash_hmac('sha512', $payload, 'webhook_secret_123');

            $response = $this->withHeaders([
                'x-paystack-signature' => $signature,
                'Content-Type' => 'application/json',
            ])->postJson('/api/v1/webhooks/paystack', json_decode($payload, true));

            $response->assertStatus(200)
                ->assertJson(['status' => 'success']);
        });

        it('processes transfer.success webhook', function () {
            $payload = json_encode([
                'event' => 'transfer.success',
                'data' => [
                    'reference' => 'transfer_ref_123',
                    'amount' => 50000,
                    'recipient' => [
                        'name' => 'John Doe',
                    ],
                ],
            ]);

            $signature = hash_hmac('sha512', $payload, 'webhook_secret_123');

            $response = $this->withHeaders([
                'x-paystack-signature' => $signature,
                'Content-Type' => 'application/json',
            ])->postJson('/api/v1/webhooks/paystack', json_decode($payload, true));

            $response->assertStatus(200)
                ->assertJson(['status' => 'success']);
        });

        it('handles unknown webhook events gracefully', function () {
            $payload = json_encode([
                'event' => 'unknown.event',
                'data' => [
                    'some' => 'data',
                ],
            ]);

            $signature = hash_hmac('sha512', $payload, 'webhook_secret_123');

            $response = $this->withHeaders([
                'x-paystack-signature' => $signature,
                'Content-Type' => 'application/json',
            ])->postJson('/api/v1/webhooks/paystack', json_decode($payload, true));

            $response->assertStatus(200)
                ->assertJson(['status' => 'success']);
        });
    });

    describe('Test Webhook', function () {
        it('can receive test webhook', function () {
            $testData = [
                'test' => true,
                'message' => 'This is a test webhook',
                'timestamp' => now()->toISOString(),
            ];

            $response = $this->postJson('/api/v1/webhooks/test', $testData);

            $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'timestamp',
                ])
                ->assertJson([
                    'status' => 'success',
                    'message' => 'Test webhook received',
                ]);
        });

        it('logs test webhook data', function () {
            $testData = [
                'test' => true,
                'custom_header' => 'test-value',
            ];

            $response = $this->withHeaders([
                'X-Custom-Header' => 'test-value',
            ])->postJson('/api/v1/webhooks/test', $testData);

            $response->assertStatus(200);

            // The webhook should log the received data
            // In a real test, you might want to assert log entries
        });

        it('accepts empty test webhook', function () {
            $response = $this->postJson('/api/v1/webhooks/test', []);

            $response->assertStatus(200)
                ->assertJson([
                    'status' => 'success',
                    'message' => 'Test webhook received',
                ]);
        });

        it('accepts GET request for test webhook', function () {
            $response = $this->getJson('/api/v1/webhooks/test');

            // This should fail since we only accept POST
            $response->assertStatus(405); // Method Not Allowed
        });
    });

    describe('Webhook Security', function () {
        it('is accessible without authentication', function () {
            // Webhooks should not require authentication
            $payload = json_encode([
                'event' => 'charge.success',
                'data' => ['reference' => 'test_ref_123'],
            ]);

            $signature = hash_hmac('sha512', $payload, 'webhook_secret_123');

            $response = $this->withHeaders([
                'x-paystack-signature' => $signature,
            ])->postJson('/api/v1/webhooks/paystack', json_decode($payload, true));

            $response->assertStatus(200);
        });

        it('validates webhook signature timing attack resistance', function () {
            $payload = json_encode([
                'event' => 'charge.success',
                'data' => ['reference' => 'test_ref_123'],
            ]);

            // Test with slightly different signatures
            $correctSignature = hash_hmac('sha512', $payload, 'webhook_secret_123');
            $incorrectSignature = substr($correctSignature, 0, -1).'x';

            // Correct signature should work
            $response1 = $this->withHeaders([
                'x-paystack-signature' => $correctSignature,
            ])->postJson('/api/v1/webhooks/paystack', json_decode($payload, true));

            $response1->assertStatus(200);

            // Incorrect signature should fail
            $response2 = $this->withHeaders([
                'x-paystack-signature' => $incorrectSignature,
            ])->postJson('/api/v1/webhooks/paystack', json_decode($payload, true));

            $response2->assertStatus(401);
        });
    });
});
